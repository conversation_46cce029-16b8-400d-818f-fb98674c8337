/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "general": {
    "password_page": {
      "login_form_heading": "<PERSON><PERSON><PERSON> toko dengan sandi:",
      "login_password_button": "Masuk dengan sandi",
      "login_form_password_label": "<PERSON><PERSON>",
      "login_form_password_placeholder": "Sandi Anda",
      "login_form_error": "Sandi salah!",
      "login_form_submit": "<PERSON>suk",
      "admin_link_html": "Anda pemilik toko? <a href=\"/admin\" class=\"link underlined-link\">Login di sini</a>",
      "powered_by_shopify_html": "Toko ini didukung oleh {{ shopify }}"
    },
    "social": {
      "alt_text": {
        "share_on_facebook": "Bagikan di Facebook",
        "share_on_twitter": "Bagikan di X",
        "share_on_pinterest": "Pin di Pinterest"
      },
      "links": {
        "twitter": "X (Twitter)",
        "facebook": "Facebook",
        "pinterest": "Pinterest",
        "instagram": "Instagram",
        "tumblr": "Tumblr",
        "snapchat": "Snapchat",
        "youtube": "YouTube",
        "vimeo": "Vimeo",
        "tiktok": "TikTok"
      }
    },
    "continue_shopping": "Lanjutkan belanja",
    "pagination": {
      "label": "Pembagian Halaman",
      "page": "Halaman {{ number }}",
      "next": "Halaman berikutnya",
      "previous": "Halaman sebelumnya"
    },
    "search": {
      "search": "Cari",
      "reset": "Hapus kata kunci pencarian"
    },
    "cart": {
      "view": "Lihat keranjang ({{ count }})",
      "item_added": "Item ditambahkan ke keranjang Anda",
      "view_empty_cart": "Lihat keranjang"
    },
    "share": {
      "copy_to_clipboard": "Salin tautan",
      "share_url": "Tautan",
      "success_message": "Tautan disalin ke clipboard",
      "close": "Tutup bagikan"
    },
    "slider": {
      "of": "dari",
      "next_slide": "Geser kanan",
      "previous_slide": "Geser kiri",
      "name": "Slider"
    }
  },
  "newsletter": {
    "label": "Email",
    "success": "Terima kasih sudah berlangganan",
    "button_label": "Berlangganan"
  },
  "accessibility": {
    "skip_to_text": "Langsung ke konten",
    "close": "Tutup",
    "unit_price_separator": "per",
    "vendor": "Vendor:",
    "error": "Kesalahan",
    "refresh_page": "Jika memilih salah satu, seluruh halaman akan dimuat ulang.",
    "link_messages": {
      "new_window": "Membuka di jendela baru.",
      "external": "Membuka situs web eksternal."
    },
    "loading": "Memuat...",
    "skip_to_product_info": "Langsung ke informasi produk",
    "total_reviews": "ulasan keseluruhan",
    "star_reviews_info": "{{ rating_value }} dari {{ rating_max }} bintang",
    "collapsible_content_title": "Konten yang dapat ditutup",
    "complementary_products": "Produk pelengkap"
  },
  "blogs": {
    "article": {
      "blog": "Blog",
      "read_more_title": "Baca selengkapnya: {{ title }}",
      "comments": {
        "one": "{{ count }} komentar",
        "other": "{{ count }} komentar"
      },
      "moderated": "Ingat, komentar perlu disetujui sebelum dipublikasikan.",
      "comment_form_title": "Tulis komentar",
      "name": "Nama",
      "email": "Email",
      "message": "Komentar",
      "post": "Posting komentar",
      "back_to_blog": "Kembali ke blog",
      "share": "Bagikan artikel ini",
      "success": "Komentar Anda berhasil diposting! Terima kasih!",
      "success_moderated": "Komentar Anda berhasil diposting. Kami akan memublikasikannya sebentar lagi, blog kami sedang dimoderasi."
    }
  },
  "onboarding": {
    "product_title": "Contoh judul produk",
    "collection_title": "Nama koleksi"
  },
  "products": {
    "product": {
      "add_to_cart": "Tambahkan ke keranjang",
      "description": "Deskripsi",
      "on_sale": "Obral",
      "product_variants": "Varian produk",
      "quantity": {
        "label": "Jumlah",
        "input_label": "Jumlah untuk {{ product }}",
        "increase": "Tambah jumlah untuk {{ product }}",
        "decrease": "Kurangi jumlah untuk {{ product }}",
        "minimum_of": "Minimum {{ quantity }}",
        "maximum_of": "Maksimum {{ quantity }}",
        "multiples_of": "Nilai peningkatan {{ quantity }}",
        "in_cart_html": "<span class=\"quantity-cart\">{{ quantity }}</span> di keranjang",
        "note": "Lihat aturan kuantitas",
        "min_of": "Min. {{ quantity }}",
        "max_of": "Maks. {{ quantity }}"
      },
      "price": {
        "from_price_html": "Dari {{ price }}",
        "regular_price": "Harga reguler",
        "sale_price": "Harga obral",
        "unit_price": "Harga satuan"
      },
      "share": "Bagikan produk ini",
      "sold_out": "Habis",
      "unavailable": "Tidak Tersedia",
      "vendor": "Vendor",
      "video_exit_message": "{{ title }} membuka video layar penuh di jendela yang sama.",
      "xr_button": "Lihat di lokasi Anda",
      "xr_button_label": "Lihat di lokasi Anda, muat item di jendela realitas tertambah",
      "pickup_availability": {
        "view_store_info": "Lihat informasi toko",
        "check_other_stores": "Periksa ketersediaan di toko lainnya",
        "pick_up_available": "Pengambilan tersedia",
        "pick_up_available_at_html": "Pengambilan dapat dilakukan di <span class=\"color-foreground\">{{ location_name }}</span>",
        "pick_up_unavailable_at_html": "Saat ini, pengambilan tidak dapat dilakukan di <span class=\"color-foreground\">{{ location_name }}</span>",
        "unavailable": "Tidak dapat memuat ketersediaan pengambilan",
        "refresh": "Muat ulang"
      },
      "media": {
        "open_media": "Buka media {{ index }} di modal",
        "play_model": "Putar Penampil 3D",
        "play_video": "Putar video",
        "gallery_viewer": "Penampil Galeri",
        "load_image": "Muat gambar {{ index }} di tampilan galeri",
        "load_model": "Muat Model 3D {{ index }} di tampilan galeri",
        "load_video": "Putar video {{ index }} di tampilan galeri",
        "image_available": "Gambar {{ index }} kini tersedia di tampilan galeri"
      },
      "view_full_details": "Lihat detail lengkap",
      "shipping_policy_html": "<a href=\"{{ link }}\">Biaya pengiriman</a> dihitung saat checkout.",
      "choose_options": "Pilih opsi",
      "choose_product_options": "Pilih opsi untuk {{ product_name }}",
      "value_unavailable": "{{ option_value }} - Tidak tersedia",
      "variant_sold_out_or_unavailable": "Varian terjual habis atau tidak tersedia",
      "inventory_in_stock": "Tersedia",
      "inventory_in_stock_show_count": "{{ quantity }} tersedia",
      "inventory_low_stock": "Stok sedikit",
      "inventory_low_stock_show_count": "Stok sedikit: {{ quantity }} tersisa",
      "inventory_out_of_stock": "Habis",
      "inventory_out_of_stock_continue_selling": "Tersedia",
      "sku": "SKU",
      "volume_pricing": {
        "title": "Harga Volume",
        "note": "Harga volume tersedia",
        "minimum": "{{ quantity }}+",
        "price_range": "{{ minimum }} - {{ maximum }}",
        "price_at_each_html": "di {{ price }}/satuan"
      },
      "taxes_included": "Termasuk pajak.",
      "duties_included": "Termasuk bea cukai.",
      "duties_and_taxes_included": "Termasuk bea cukai dan pajak."
    },
    "modal": {
      "label": "Galeri media"
    },
    "facets": {
      "apply": "Pakai",
      "clear": "Hapus",
      "clear_all": "Hapus semua",
      "from": "Dari",
      "filter_and_sort": "Filter dan urutkan",
      "filter_by_label": "Filter:",
      "filter_button": "Filter",
      "filters_selected": {
        "one": "{{ count }} dipilih",
        "other": "{{ count }} dipilih"
      },
      "max_price": "Harga tertinggi adalah {{ price }}",
      "product_count": {
        "one": "{{ product_count }} dari {{ count }} produk",
        "other": "{{ product_count }} dari {{ count }} produk"
      },
      "product_count_simple": {
        "one": "{{ count }} produk",
        "other": "{{ count }} produk"
      },
      "reset": "Reset",
      "sort_button": "Urutkan",
      "sort_by_label": "Urutkan berdasarkan:",
      "to": "Sampai",
      "clear_filter": "Hapus filter",
      "filter_selected_accessibility": "{{ type }} ({{ count }} filter dipilih)",
      "show_more": "Selengkapnya",
      "show_less": "Sembunyikan lainnya",
      "filter_and_operator_subtitle": "Cocokkan semua"
    }
  },
  "templates": {
    "404": {
      "title": "Halaman tidak ditemukan",
      "subtext": "404"
    },
    "search": {
      "no_results": "Tidak ada hasil ditemukan untuk “{{ terms }}”. Periksa ejaan atau gunakan kata atau frasa yang berbeda.",
      "results_with_count": {
        "one": "{{ count }} hasil",
        "other": "{{ count }} hasil"
      },
      "title": "Hasil pencarian",
      "page": "Halaman",
      "products": "Produk",
      "search_for": "Cari “{{ terms }}”",
      "results_with_count_and_term": {
        "one": "{{ count }} hasil ditemukan untuk “{{ terms }}”",
        "other": "{{ count }} hasil ditemukan untuk “{{ terms }}”"
      },
      "results_pages_with_count": {
        "one": "{{ count }} halaman",
        "other": "{{ count }} halaman"
      },
      "results_products_with_count": {
        "one": "{{ count }} produk",
        "other": "{{ count }} produk"
      },
      "suggestions": "Saran",
      "pages": "Halaman",
      "results_suggestions_with_count": {
        "one": "{{ count }} saran",
        "other": "{{ count }} saran"
      }
    },
    "cart": {
      "cart": "Keranjang"
    },
    "contact": {
      "form": {
        "name": "Nama",
        "email": "Email",
        "phone": "Nomor telepon",
        "comment": "Komentar",
        "send": "Kirim",
        "post_success": "Terima kasih sudah menghubungi kami. Kami akan segera menghubungi Anda.",
        "error_heading": "Mohon sesuaikan:",
        "title": "Formulir kontak"
      }
    }
  },
  "sections": {
    "header": {
      "announcement": "Pengumuman",
      "menu": "Menu",
      "cart_count": {
        "one": "{{ count }} item",
        "other": "{{ count }} item"
      }
    },
    "cart": {
      "title": "Keranjang Anda",
      "caption": "Item di keranjang",
      "remove_title": "Hapus {{ title }}",
      "note": "Instruksi khusus pesanan",
      "checkout": "Check out",
      "empty": "Keranjang Anda kosong",
      "cart_error": "Terjadi kesalahan saat memperbarui keranjang. Silakan coba lagi.",
      "cart_quantity_error_html": "Hanya dapat menambahkan {{ quantity }} item ini ke keranjang Anda.",
      "headings": {
        "product": "Produk",
        "price": "Harga",
        "total": "Total",
        "quantity": "Jumlah",
        "image": "Gambar produk"
      },
      "update": "Perbarui",
      "login": {
        "title": "Sudah punya akun?",
        "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Login</a> untuk checkout lebih cepat."
      },
      "estimated_total": "Estimasi total",
      "new_estimated_total": "Estimasi total baru",
      "duties_and_taxes_included_shipping_at_checkout_with_policy_html": "Termasuk bea cukai dan pajak. Diskon dan <a href=\"{{ link }}\">biaya pengiriman</a> dihitung saat checkout.",
      "duties_and_taxes_included_shipping_at_checkout_without_policy": "Termasuk bea cukai dan pajak. Diskon dan biaya pengiriman dihitung saat checkout.",
      "taxes_included_shipping_at_checkout_with_policy_html": "Termasuk pajak. Diskon dan <a href=\"{{ link }}\">biaya pengiriman</a> dihitung saat checkout.",
      "taxes_included_shipping_at_checkout_without_policy": "Termasuk pajak. Diskon dan biaya pengiriman dihitung saat checkout.",
      "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_html": "Termasuk bea cukai. Pajak, diskon, dan <a href=\"{{ link }}\">biaya pengiriman</a> dihitung saat checkout.",
      "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy": "Termasuk bea cukai. Pajak, diskon, dan biaya pengiriman dihitung saat checkout.",
      "taxes_at_checkout_shipping_at_checkout_with_policy_html": "Pajak, diskon, dan <a href=\"{{ link }}\">biaya pengiriman</a> dihitung saat checkout.",
      "taxes_at_checkout_shipping_at_checkout_without_policy": "Pajak, diskon, dan biaya pengiriman dihitung saat checkout."
    },
    "footer": {
      "payment": "Metode pembayaran"
    },
    "featured_blog": {
      "view_all": "Lihat semua",
      "onboarding_title": "Postingan blog",
      "onboarding_content": "Berikan pelanggan ringkasan postingan blog Anda"
    },
    "featured_collection": {
      "view_all": "Lihat semua",
      "view_all_label": "Lihat semua produk dalam koleksi {{ collection_name }}"
    },
    "collection_list": {
      "view_all": "Lihat semua"
    },
    "collection_template": {
      "title": "Koleksi",
      "empty": "Tidak ada produk yang ditemukan",
      "use_fewer_filters_html": "Gunakan lebih sedikit filter atau <a class=\"{{ class }}\" href=\"{{ link }}\">hapus semua</a>"
    },
    "video": {
      "load_video": "Muat video: {{ description }}"
    },
    "slideshow": {
      "load_slide": "Muat slide",
      "previous_slideshow": "Slide sebelumnya",
      "next_slideshow": "Slide berikutnya",
      "pause_slideshow": "Jeda slideshow",
      "play_slideshow": "Putar slideshow",
      "carousel": "Carousel",
      "slide": "Geser"
    },
    "page": {
      "title": "Judul halaman"
    },
    "announcements": {
      "previous_announcement": "Pengumuman sebelumnya",
      "next_announcement": "Pengumuman selanjutnya",
      "carousel": "Carousel",
      "announcement": "Pengumuman",
      "announcement_bar": "Bilah pengumuman"
    },
    "quick_order_list": {
      "product_total": "Subtotal produk",
      "view_cart": "Lihat keranjang",
      "each": "{{ money }}/satuan",
      "product": "Produk",
      "variant": "Varian",
      "variant_total": "Total varian",
      "items_added": {
        "one": "{{ quantity }} item ditambahkan",
        "other": "{{ quantity }} item ditambahkan"
      },
      "items_removed": {
        "one": "{{ quantity }} item dihapus",
        "other": "{{ quantity }} item dihapus"
      },
      "product_variants": "Varian produk",
      "total_items": "Total item",
      "remove_all_items_confirmation": "Hapus semua item yang berjumlah {{ quantity }} dari keranjang Anda?",
      "remove_all": "Hapus semuanya",
      "cancel": "Batalkan",
      "remove_all_single_item_confirmation": "Hapus 1 item dari keranjang Anda?",
      "min_error": "Item ini memiliki minimal {{ min }}",
      "max_error": "Item ini memiliki maksimum {{ max }}",
      "step_error": "Anda hanya dapat menambahkan item ini secara inkremental sebesar {{ step }}"
    }
  },
  "localization": {
    "country_label": "Negara/Wilayah",
    "language_label": "Bahasa",
    "update_language": "Perbarui bahasa",
    "update_country": "Perbarui negara/wilayah",
    "search": "Cari",
    "popular_countries_regions": "Negara/wilayah populer",
    "country_results_count": "{{ count }} negara/wilayah ditemukan"
  },
  "customer": {
    "account": {
      "title": "Akun",
      "details": "Detail akun",
      "view_addresses": "Lihat alamat",
      "return": "Kembali ke detail Akun"
    },
    "account_fallback": "Akun",
    "activate_account": {
      "title": "Aktifkan akun",
      "subtext": "Buat sandi untuk mengaktifkan akunmu.",
      "password": "Sandi",
      "password_confirm": "Konfirmasi sandi",
      "submit": "Aktifkan akun",
      "cancel": "Tolak undangan"
    },
    "addresses": {
      "title": "Alamat",
      "default": "Default",
      "add_new": "Tambahkan alamat baru",
      "edit_address": "Edit alamat",
      "first_name": "Nama depan",
      "last_name": "Nama belakang",
      "company": "Perusahaan",
      "address1": "Alamat 1",
      "address2": "Alamat 2",
      "city": "Kota",
      "country": "Negara/Wilayah",
      "province": "Provinsi",
      "zip": "Kode pos",
      "phone": "Telepon",
      "set_default": "Atur sebagai alamat default",
      "add": "Tambahkan alamat",
      "update": "Perbarui alamat",
      "cancel": "Batal",
      "edit": "Edit",
      "delete": "Hapus",
      "delete_confirm": "Yakin ingin menghapus alamat ini?"
    },
    "log_in": "Login",
    "log_out": "Logout",
    "login_page": {
      "cancel": "Batal",
      "create_account": "Buat Akun",
      "email": "Email",
      "forgot_password": "Lupa sandi?",
      "guest_continue": "Lanjutkan",
      "guest_title": "Lanjutkan sebagai tamu",
      "password": "Sandi",
      "title": "Login",
      "sign_in": "Masuk",
      "submit": "Kirim",
      "alternate_provider_separator": "atau"
    },
    "orders": {
      "title": "Riwayat pesanan",
      "order_number": "Pesanan",
      "order_number_link": "Nomor pesanan {{ number }}",
      "date": "Tanggal",
      "payment_status": "Status pembayaran",
      "fulfillment_status": "Status pesanan",
      "total": "Total",
      "none": "Kamu belum membuat pesanan."
    },
    "recover_password": {
      "title": "Reset sandi",
      "subtext": "Kami akan mengirimi Anda email untuk mereset sandi",
      "success": "Kami telah mengirimi Anda email berisi tautan untuk memperbarui sandi."
    },
    "register": {
      "title": "Buat Akun",
      "first_name": "Nama depan",
      "last_name": "Nama belakang",
      "email": "Email",
      "password": "Sandi",
      "submit": "Buat"
    },
    "reset_password": {
      "title": "Reset sandi akun",
      "subtext": "Masukkan sandi baru",
      "password": "Sandi",
      "password_confirm": "Konfirmasi sandi",
      "submit": "Reset sandi"
    },
    "order": {
      "title": "Pesanan {{ name }}",
      "date_html": "Dibuat pada {{ date }}",
      "cancelled_html": "Pesanan Dibatalkan pada {{ date }}",
      "cancelled_reason": "Alasan: {{ reason }}",
      "billing_address": "Alamat Penagihan",
      "payment_status": "Status Pembayaran",
      "shipping_address": "Alamat Pengiriman",
      "fulfillment_status": "Status Pesanan",
      "discount": "Diskon",
      "shipping": "Pengiriman",
      "tax": "Pajak",
      "product": "Produk",
      "sku": "SKU",
      "price": "Harga",
      "quantity": "Jumlah",
      "total": "Total",
      "fulfilled_at_html": "Selesai pada {{ date }}",
      "track_shipment": "Lacak pengiriman",
      "tracking_url": "Tautan pelacakan",
      "tracking_company": "Kurir",
      "tracking_number": "Nomor pelacakan",
      "subtotal": "Subtotal",
      "total_duties": "Bea cukai",
      "total_refunded": "Dana dikembalikan"
    }
  },
  "gift_cards": {
    "issued": {
      "title": "Ini dia voucher senilai {{ value }} Anda untuk {{ shop }}!",
      "subtext": "Voucher Anda",
      "gift_card_code": "Kode voucher",
      "shop_link": "Kunjungi toko online",
      "add_to_apple_wallet": "Tambahkan ke Apple Wallet",
      "qr_image_alt": "Kode QR — pindai untuk menukarkan voucher",
      "copy_code": "Salin kode voucher",
      "expired": "Kedaluwarsa",
      "copy_code_success": "Kode berhasil disalin",
      "how_to_use_gift_card": "Gunakan kode voucher secara online atau kode QR di toko",
      "expiration_date": "Kedaluwarsa pada {{ expires_on }}"
    }
  },
  "recipient": {
    "form": {
      "checkbox": "Saya ingin mengirim ini sebagai hadiah",
      "email_label": "Email penerima",
      "email": "Email",
      "name_label": "Nama penerima (opsional)",
      "name": "Nama",
      "message_label": "Pesan (opsional)",
      "message": "Pesan",
      "max_characters": "Maksimum {{ max_chars }} karakter",
      "email_label_optional_for_no_js_behavior": "Email penerima (opsional)",
      "send_on": "TTTT-BB-HH",
      "send_on_label": "Kirim pada (opsional)",
      "expanded": "Formulir penerima voucher dibuka",
      "collapsed": "Formulir penerima voucher ditutup"
    }
  },
  "pagefly": {
    "products": {
      "product": {
        "regular_price": "Regular price",
        "sold_out": "Sold out",
        "unavailable": "Unavailable",
        "on_sale": "Sale",
        "quantity": "Quantity",
        "add_to_cart": "Add to cart",
        "back_to_collection": "Back to {{ title }}",
        "view_details": "View details"
      }
    },
    "article": {
      "tags": "Tags:",
      "all_topics": "All topics",
      "by_author": "by {{ author }}",
      "posted_in": "Posted in",
      "read_more": "Read more",
      "back_to_blog": "Back to {{ title }}"
    },
    "comments": {
      "title": "Leave a comment",
      "name": "Name",
      "email": "Email",
      "message": "Message",
      "post": "Post comment",
      "moderated": "Please note, comments must be approved before they are published",
      "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.",
      "success": "Your comment was posted successfully! Thank you!",
      "comments_with_count": {
        "one": "{{ count }} comment",
        "other": "{{ count }} comments"
      }
    },
    "password_page": {
      "login_form_message": "Enter store using password:",
      "login_form_password_label": "Password",
      "login_form_password_placeholder": "Your password",
      "login_form_submit": "Enter",
      "signup_form_email_label": "Email",
      "signup_form_success": "We will send you an email right before we open!",
      "password_link": "Enter using password"
    }
  }
}