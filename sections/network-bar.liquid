{% comment %}
  Network Bar Section - Barra de navegación editable y localizable
  Versión: 2.8 (Improved translation fallbacks for ARIA labels)
{% endcomment %}

<link href="https://fonts.googleapis.com/css2?family=Dela+Gothic+One&display=swap" rel="stylesheet">

<style>
  .network-bar-section {
    font-family: 'Dela Gothic One', cursive;
  }

  .network-bar-section *,
  .network-bar-section *::before,
  .network-bar-section *::after {
    box-sizing: border-box;
  }

  .network-bar-section .global-nav {
    background: {% if section.settings.background_gradient and section.settings.background_gradient != blank %}{{ section.settings.background_gradient }}{% else %}linear-gradient(to right, rgb(0, 48, 102), rgb(0, 150, 136)){% endif %};
    padding: 8px 0;
    {% if section.settings.sticky_nav %}
      position: sticky;
      top: 0;
    {% endif %}
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    z-index: 9999;
    position: relative;
  }

  .network-bar-section .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    gap: 20px;
    min-height: 46px;
  }

  .network-bar-section .logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    flex-shrink: 0;
  }

  .network-bar-section .logo img {
    height: 30px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
  }

  .network-bar-section .brand-links {
    display: flex;
    gap: 15px;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
    flex-wrap: nowrap;
    overflow: hidden;
  }

  .network-bar-section .brand-links li {
    white-space: nowrap;
    flex-shrink: 0;
  }

  .network-bar-section .brand-links a {
    color: white;
    text-decoration: none;
    font-size: 12px;
    font-weight: 400;
    padding: 6px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
    display: block;
    overflow: hidden;
  }

  .network-bar-section .brand-links a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 1));
    transition: width 0.3s ease;
  }

  .network-bar-section .brand-links a:hover::after,
  .network-bar-section .brand-links a:focus::after {
    width: 100%;
  }

  .network-bar-section .brand-links a:hover,
  .network-bar-section .brand-links a:focus {
    background-color: rgba(255, 255, 255, 0.1);
    outline: none;
  }

  /* More Switcher Styles (exact copy of language switcher) */
  .network-bar-section .more-switcher {
    position: relative;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    z-index: 1000;
  }

  .network-bar-section .more-switcher.active {
    z-index: 10000;
  }

  .network-bar-section .more-button {
    background: none;
    border: none;
    color: white;
    font-size: 12px;
    font-weight: 400;
    font-family: inherit;
    padding: 6px 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    position: relative;
    overflow: hidden;
    min-width: 80px;
    max-width: 120px;
    justify-content: center;
  }

  .network-bar-section .more-button:hover,
  .network-bar-section .more-button:focus {
    background-color: rgba(255, 255, 255, 0.1);
    outline: none;
  }

  .network-bar-section .more-button::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 1));
    transition: width 0.3s ease;
  }

  .network-bar-section .more-button:hover::after,
  .network-bar-section .more-button:focus::after {
    width: 100%;
  }

  .network-bar-section .more-name {
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
  }

  .network-bar-section .more-arrow {
    font-size: 10px;
    transition: transform 0.3s ease;
    line-height: 1;
  }

  .network-bar-section .more-switcher.active .more-arrow {
    transform: rotate(180deg);
  }

  .network-bar-section .more-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background: white;
    min-width: 160px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    padding: 8px 0;
    z-index: 10001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .network-bar-section .more-switcher.active .more-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .network-bar-section .more-option {
    display: block;
    color: #1a1a1a;
    background: white;
    padding: 10px 16px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    transition: background-color 0.2s ease;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-family: inherit;
    position: relative;
    z-index: 10002;
  }

  .network-bar-section .more-option:hover,
  .network-bar-section .more-option:focus {
    background-color: #f8f9fa;
    color: #1a1a1a;
    outline: none;
    z-index: 10003;
  }

  .network-bar-section .more-option:focus-visible {
    outline: 2px solid rgb(0, 48, 102);
    outline-offset: -2px;
  }

  /* Mobile First Responsive */
  @media (min-width: 480px) {
    .network-bar-section .nav-container {
      padding: 0 20px;
      gap: 25px;
    }

    .network-bar-section .logo img {
      height: 35px;
    }

    .network-bar-section .brand-links {
      gap: 20px;
    }

    .network-bar-section .brand-links a {
      font-size: 13px;
      padding: 7px 14px;
    }

    .network-bar-section .language-button {
      font-size: 13px;
      padding: 7px 14px;
      min-width: 90px;
      max-width: 140px;
    }



    .network-bar-section .language-dropdown {
      min-width: 180px;
    }

    .network-bar-section .language-option {
      padding: 12px 18px;
      font-size: 13px;
    }
  }

  @media (min-width: 768px) {
    .network-bar-section .nav-container {
      gap: 30px;
    }

    .network-bar-section .logo img {
      height: 40px;
    }

    .network-bar-section .brand-links {
      gap: 25px;
    }

    .network-bar-section .brand-links a {
      font-size: 14px;
      padding: 8px 16px;
    }

    .network-bar-section .language-button {
      font-size: 14px;
      padding: 8px 16px;
      min-width: 100px;
      max-width: 160px;
    }

    .network-bar-section .brand-links li:nth-child(n+2):not(.dropdown) {
      display: block;
    }



    .network-bar-section .language-dropdown {
      min-width: 200px;
    }

    .network-bar-section .language-option {
      padding: 12px 20px;
      font-size: 14px;
    }
  }

  @media (min-width: 900px) {
    .network-bar-section .brand-links {
      gap: 30px;
    }

    .network-bar-section .brand-links li:nth-child(n+3):not(.dropdown) {
      display: block;
    }
  }

  @media (min-width: 1024px) {
    .network-bar-section .brand-links li:nth-child(n+4):not(.dropdown) {
      display: block;
    }
  }

  /* Hide items on smaller screens */
  @media (max-width: 767px) {
    .network-bar-section .brand-links li:nth-child(n+2):not(.dropdown) {
      display: none;
    }
  }

  @media (max-width: 899px) {
    .network-bar-section .brand-links li:nth-child(n+3):not(.dropdown) {
      display: none;
    }
  }

  @media (max-width: 1023px) {
    .network-bar-section .brand-links li:nth-child(n+4):not(.dropdown) {
      display: none;
    }
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .network-bar-section * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Focus visible for keyboard navigation */
  .network-bar-section .brand-links a:focus-visible {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
  }

  /* Language Switcher Styles */
  .network-bar-section .language-switcher {
    position: relative;
    display: flex;
    align-items: center;
    margin-left: auto;
    flex-shrink: 0;
    z-index: 1000;
  }

  .network-bar-section .language-switcher.active {
    z-index: 10000;
  }

  .network-bar-section .language-button {
    background: none;
    border: none;
    color: white;
    font-size: 12px;
    font-weight: 400;
    font-family: inherit;
    padding: 6px 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    position: relative;
    overflow: hidden;
    min-width: 80px;
    max-width: 120px;
    justify-content: center;
  }

  .network-bar-section .language-button:hover,
  .network-bar-section .language-button:focus {
    background-color: rgba(255, 255, 255, 0.1);
    outline: none;
  }

  .network-bar-section .language-button::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 1));
    transition: width 0.3s ease;
  }

  .network-bar-section .language-button:hover::after,
  .network-bar-section .language-button:focus::after {
    width: 100%;
  }

  .network-bar-section .language-name {
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
  }

  .network-bar-section .language-arrow {
    font-size: 10px;
    transition: transform 0.3s ease;
    line-height: 1;
  }

  .network-bar-section .language-switcher.active .language-arrow {
    transform: rotate(180deg);
  }

  .network-bar-section .language-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background: white;
    min-width: 160px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    padding: 8px 0;
    z-index: 10001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .network-bar-section .language-switcher.active .language-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .network-bar-section .language-option {
    display: block;
    color: #1a1a1a;
    background: white;
    padding: 10px 16px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    transition: background-color 0.2s ease;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-family: inherit;
    position: relative;
    z-index: 10002;
  }

  .network-bar-section .language-option:hover,
  .network-bar-section .language-option:focus {
    background-color: #f8f9fa;
    color: #1a1a1a;
    outline: none;
    z-index: 10003;
  }

  .network-bar-section .language-option.current {
    background-color: #e3f2fd;
    color: rgb(0, 48, 102);
    font-weight: 700;
  }

  .network-bar-section .language-option:focus-visible {
    outline: 2px solid rgb(0, 48, 102);
    outline-offset: -2px;
  }
</style>

<nav
  class="global-nav"
  role="navigation"
  aria-label="{{ 'sections.network_bar.navigation_label' | t: default: section.settings.navigation_label }}"
>
  <div class="nav-container">
    <a
      href="{{ section.settings.logo_url | default: routes.root_url }}"
      class="logo"
      aria-label="{{ section.settings.logo_alt | default: shop.name | escape }}"
    >
      {% if section.settings.logo_image %}
        {%- assign logo_alt_text = section.settings.logo_alt | default: shop.name | escape -%}
        {{-
          section.settings.logo_image | image_url: width: 200 | image_tag:
          loading: 'eager',
          alt: logo_alt_text,
          sizes: '(max-width: 480px) 120px, (max-width: 768px) 140px, 160px'
        -}}
      {% else %}
        <img
          src="https://super-fan.club/cdn/shop/files/SuperFanLogo_100x40_crop_center.png?v=1729213169"
          alt="{{ section.settings.logo_alt | default: shop.name | escape }}"
          width="100"
          height="40"
          loading="eager"
        >
      {% endif %}
    </a>

    {% if section.blocks.size > 0 %}
      <ul class="brand-links">
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'brand_link' %}
              {% if block.settings.title != blank %}
                <li {{ block.shopify_attributes }}>
                  <a
                    href="{{ block.settings.url | default: '#' }}"
                    {% if block.settings.url == blank %}
                      aria-disabled="true"
                    {% endif %}
                  >
                    {{ block.settings.title | escape }}
                  </a>
                </li>
              {% endif %}

            {% when 'dropdown' %}
              {% comment %} Store dropdown data for the more switcher outside the list {% endcomment %}
              {% if block.settings.dropdown_title != blank %}
                {% assign dropdown_title = block.settings.dropdown_title %}
                {% assign dropdown_links = '' %}
                {% for i in (1..9) %}
                  {% assign link_title_key = 'dropdown_link_' | append: i | append: '_title' %}
                  {% assign link_url_key = 'dropdown_link_' | append: i | append: '_url' %}
                  {% assign link_title = block.settings[link_title_key] %}
                  {% assign link_url = block.settings[link_url_key] %}
                  {% if link_title != blank %}
                    {% capture dropdown_link %}
                      <a href="{{ link_url | default: '#' }}" class="more-option"{% if link_url == blank %} aria-disabled="true"{% endif %}>{{ link_title | escape }}</a>
                    {% endcapture %}
                    {% assign dropdown_links = dropdown_links | append: dropdown_link %}
                  {% endif %}
                {% endfor %}
              {% endif %}
          {% endcase %}
        {% endfor %}
      </ul>
    {% endif %}

    {% comment %} More Switcher (always visible) {% endcomment %}
    {% if dropdown_title and dropdown_links != blank %}
      <div class="more-switcher">
        <button
          class="more-button"
          aria-expanded="false"
          aria-haspopup="true"
          aria-label="{{ dropdown_title | escape }} {{ 'sections.network_bar.menu_label' | t: default: section.settings.menu_label }}"
        >
          <span class="more-name">{{ dropdown_title | escape }}</span>
          <span class="more-arrow" aria-hidden="true">▼</span>
        </button>
        <div class="more-dropdown">
          {{ dropdown_links }}
        </div>
      </div>
    {% endif %}

    {% comment %} Language Switcher {% endcomment %}
    {% if section.settings.enable_language_selector and localization.available_languages.size > 1 %}
      <div class="language-switcher">
        <button
          class="language-button"
          aria-expanded="false"
          aria-haspopup="true"
          aria-label="{{ 'localization.language_label' | t }}"
        >
          <span class="language-name">{{ localization.language.endonym_name }}</span>
          <span class="language-arrow" aria-hidden="true">▼</span>
        </button>
        <div class="language-dropdown">
          {% for language in localization.available_languages %}
            <button
              class="language-option{% if language.iso_code == localization.language.iso_code %} current{% endif %}"
              data-language-code="{{ language.iso_code }}"
              data-language-name="{{ language.endonym_name }}"
              {% if language.iso_code == localization.language.iso_code %}
                aria-current="true"
              {% endif %}
            >
              {{ language.endonym_name }}
            </button>
          {% endfor %}
        </div>
      </div>
    {% endif %}
  </div>
</nav>

<script>
  (function() {
    'use strict';

    // Function to toggle more switcher (exact copy of language switcher logic)
    function toggleMoreSwitcher(buttonElement) {
      console.log('[NetworkBar] toggleMoreSwitcher called for button:', buttonElement);
      if (!buttonElement) {
        console.error('[NetworkBar] No button element provided to toggleMoreSwitcher.');
        return;
      }

      const moreSwitcher = buttonElement.closest('.more-switcher');
      if (!moreSwitcher) {
        console.error('[NetworkBar] Could not find parent .more-switcher element for button:', buttonElement);
        return;
      }

      const isActive = moreSwitcher.classList.contains('active');
      const languageSwitcher = document.querySelector('.language-switcher');

      // Close language switcher if open
      if (languageSwitcher && languageSwitcher.classList.contains('active')) {
        languageSwitcher.classList.remove('active');
        const languageButton = languageSwitcher.querySelector('.language-button');
        if (languageButton) {
          languageButton.setAttribute('aria-expanded', 'false');
        }
      }

      // Toggle the more switcher
      if (isActive) {
        moreSwitcher.classList.remove('active');
        buttonElement.setAttribute('aria-expanded', 'false');
        console.log('[NetworkBar] More switcher closed');
      } else {
        moreSwitcher.classList.add('active');
        buttonElement.setAttribute('aria-expanded', 'true');
        console.log('[NetworkBar] More switcher opened');
      }
    }

    // Function to toggle language switcher
    function toggleLanguageSwitcher(buttonElement) {
      console.log('[NetworkBar] toggleLanguageSwitcher called for button:', buttonElement);
      if (!buttonElement) {
        console.error('[NetworkBar] No button element provided to toggleLanguageSwitcher.');
        return;
      }

      const languageSwitcher = buttonElement.closest('.language-switcher');
      if (!languageSwitcher) {
        console.error('[NetworkBar] Could not find parent .language-switcher element for button:', buttonElement);
        return;
      }

      const isActive = languageSwitcher.classList.contains('active');
      const moreSwitcher = document.querySelector('.more-switcher');

      // Close more switcher if open
      if (moreSwitcher && moreSwitcher.classList.contains('active')) {
        moreSwitcher.classList.remove('active');
        const moreButton = moreSwitcher.querySelector('.more-button');
        if (moreButton) {
          moreButton.setAttribute('aria-expanded', 'false');
        }
      }

      // Toggle the language switcher
      if (isActive) {
        languageSwitcher.classList.remove('active');
        buttonElement.setAttribute('aria-expanded', 'false');
        console.log('[NetworkBar] Language switcher closed');
      } else {
        languageSwitcher.classList.add('active');
        buttonElement.setAttribute('aria-expanded', 'true');
        console.log('[NetworkBar] Language switcher opened');
      }
    }

    // Function to handle language selection
    function handleLanguageSelection(optionElement) {
      const languageCode = optionElement.getAttribute('data-language-code');
      const languageName = optionElement.getAttribute('data-language-name');

      console.log('[NetworkBar] Language selected:', languageCode, languageName);

      // Create and submit a localization form
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '/localization';

      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = 'language_code';
      input.value = languageCode;

      const returnToInput = document.createElement('input');
      returnToInput.type = 'hidden';
      returnToInput.name = 'return_to';
      returnToInput.value = window.location.pathname + window.location.search;

      form.appendChild(input);
      form.appendChild(returnToInput);
      document.body.appendChild(form);
      form.submit();
    }

    // Function to close dropdowns when clicking outside
    function handleOutsideClick(event) {
      const activeMoreSwitcher = document.querySelector('.more-switcher.active');
      const activeLanguageSwitcher = document.querySelector('.language-switcher.active');

      // Check if the click is outside the active more switcher
      if (activeMoreSwitcher && !activeMoreSwitcher.contains(event.target) && !event.target.closest('.more-button')) {
        console.log('[NetworkBar] Clicked outside active more switcher. Closing it.');
        const buttonElement = activeMoreSwitcher.querySelector('.more-button');
        activeMoreSwitcher.classList.remove('active');
        if (buttonElement) {
          buttonElement.setAttribute('aria-expanded', 'false');
        }
      }

      // Check if the click is outside the active language switcher
      if (activeLanguageSwitcher && !activeLanguageSwitcher.contains(event.target) && !event.target.closest('.language-button')) {
        console.log('[NetworkBar] Clicked outside active language switcher. Closing it.');
        const languageButton = activeLanguageSwitcher.querySelector('.language-button');
        activeLanguageSwitcher.classList.remove('active');
        if (languageButton) {
          languageButton.setAttribute('aria-expanded', 'false');
        }
      }
    }

    // Function to close dropdowns with the Escape key
    function handleKeydown(event) {
      if (event.key === 'Escape') {
        const activeMoreSwitcher = document.querySelector('.more-switcher.active');
        const activeLanguageSwitcher = document.querySelector('.language-switcher.active');

        if (activeMoreSwitcher) {
          console.log('[NetworkBar] Escape key pressed. Closing active more switcher.');
          const buttonElement = activeMoreSwitcher.querySelector('.more-button');
          activeMoreSwitcher.classList.remove('active');
          if (buttonElement) {
            buttonElement.setAttribute('aria-expanded', 'false');
            buttonElement.focus(); // Return focus to the button
          }
        }

        if (activeLanguageSwitcher) {
          console.log('[NetworkBar] Escape key pressed. Closing active language switcher.');
          const languageButton = activeLanguageSwitcher.querySelector('.language-button');
          activeLanguageSwitcher.classList.remove('active');
          if (languageButton) {
            languageButton.setAttribute('aria-expanded', 'false');
            languageButton.focus(); // Return focus to the button
          }
        }
      }
    }

    // Store original dropdown links globally
    let originalDropdownLinks = [];

    // Function to update more dropdown with hidden brand links
    function updateMoreDropdown() {
      const brandLinks = document.querySelectorAll('.brand-links li');
      const moreDropdown = document.querySelector('.more-dropdown');
      const moreSwitcher = document.querySelector('.more-switcher');

      if (!moreDropdown || !moreSwitcher) return;

      // Clear current dropdown content
      moreDropdown.innerHTML = '';

      // First, add hidden brand links at the TOP
      let hasHiddenLinks = false;
      brandLinks.forEach((li, index) => {
        const link = li.querySelector('a');
        if (link && window.getComputedStyle(li).display === 'none') {
          hasHiddenLinks = true;

          // Create a copy of the hidden link for the more dropdown
          const moreOption = document.createElement('a');
          moreOption.href = link.href;
          moreOption.className = 'more-option';
          moreOption.textContent = link.textContent;
          moreOption.setAttribute('data-responsive', 'true');

          // Copy any aria attributes
          if (link.hasAttribute('aria-disabled')) {
            moreOption.setAttribute('aria-disabled', link.getAttribute('aria-disabled'));
          }

          moreDropdown.appendChild(moreOption);
        }
      });

      // Add a separator if we have both hidden links and original links
      if (hasHiddenLinks && originalDropdownLinks.length > 0) {
        const separator = document.createElement('div');
        separator.className = 'more-separator';
        separator.style.cssText = 'height: 1px; background: #eee; margin: 8px 0;';
        moreDropdown.appendChild(separator);
      }

      // Then, add original dropdown links AFTER the hidden brand links
      originalDropdownLinks.forEach(linkData => {
        const moreOption = document.createElement('a');
        moreOption.href = linkData.href;
        moreOption.className = 'more-option';
        moreOption.textContent = linkData.text;
        moreOption.setAttribute('data-original', 'true');

        if (linkData.ariaDisabled) {
          moreOption.setAttribute('aria-disabled', 'true');
        }

        moreDropdown.appendChild(moreOption);
      });

      // Show/hide the more switcher based on whether there are any links to show
      const hasOriginalLinks = originalDropdownLinks.length > 0;
      if (hasOriginalLinks || hasHiddenLinks) {
        moreSwitcher.style.display = 'flex';
      } else {
        moreSwitcher.style.display = 'none';
      }

      console.log('[NetworkBar] More dropdown updated. Original links:', hasOriginalLinks, 'Hidden links added:', hasHiddenLinks);
    }

    // Initialize network bar functionality
    function initializeNetworkBar() {
      console.log('[NetworkBar] Initializing network bar script...');

      const moreButton = document.querySelector('.more-button');
      const languageButton = document.querySelector('.language-button');
      const languageOptions = document.querySelectorAll('.language-option');

      console.log('[NetworkBar] Found more button:', moreButton);

      // Store original dropdown links before any modifications
      const originalMoreOptions = document.querySelectorAll('.more-option');
      originalDropdownLinks = [];
      originalMoreOptions.forEach(option => {
        originalDropdownLinks.push({
          href: option.href,
          text: option.textContent,
          ariaDisabled: option.hasAttribute('aria-disabled')
        });
      });
      console.log('[NetworkBar] Stored', originalDropdownLinks.length, 'original dropdown links');

      // Add more switcher event listener
      if (moreButton) {
        moreButton.addEventListener('click', function(event) {
          event.preventDefault();
          event.stopPropagation();
          console.log('[NetworkBar] More button clicked:', this);
          toggleMoreSwitcher(this);
        });
        console.log('[NetworkBar] Click event listener added to more button');
      }

      // Initial update of more dropdown
      updateMoreDropdown();

      // Update more dropdown on window resize
      let resizeTimeout;
      window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(updateMoreDropdown, 100);
      });

      // Add language switcher event listeners
      if (languageButton) {
        languageButton.addEventListener('click', function(event) {
          event.preventDefault();
          event.stopPropagation();
          console.log('[NetworkBar] Language button clicked:', this);
          toggleLanguageSwitcher(this);
        });
        console.log('[NetworkBar] Click event listener added to language button');
      }

      languageOptions.forEach(option => {
        option.addEventListener('click', function(event) {
          handleLanguageSelection(this);
        });
        console.log('[NetworkBar] Click event listener added to language option:', option);
      });

      // Add global listeners only once
      // A flag can prevent re-adding these if initializeNetworkBar is called multiple times.
      if (!window.networkBarGlobalListenersAdded) {
        document.addEventListener('click', handleOutsideClick);
        document.addEventListener('keydown', handleKeydown);
        window.networkBarGlobalListenersAdded = true;
        console.log('[NetworkBar] Global click and keydown listeners added.');
      }
    }

    // Cleanup function
    function cleanupNetworkBar() {
      console.log('[NetworkBar] Cleaning up network bar script listeners...');
      document.removeEventListener('click', handleOutsideClick);
      document.removeEventListener('keydown', handleKeydown);
      window.removeEventListener('resize', updateMoreDropdown);
      window.networkBarGlobalListenersAdded = false; // Reset flag

      const moreButton = document.querySelector('.more-button');
      // To properly remove, we'd need the exact same function reference.
      // This is a simplification; robust listener removal is more complex.
      // For now, the main concern is the global listeners and avoiding duplicate initializations.
      console.log('[NetworkBar] Global listeners removed. Button listeners would need more specific removal if re-added frequently.');
    }

    // --- Shopify Theme Editor Integration & DOM Ready ---
    if (typeof Shopify !== 'undefined' && Shopify.designMode) {
      // Theme editor specific lifecycle
      document.addEventListener('shopify:section:load', function(event) {
        const sectionElement = event.target;
        // Check if the loaded section is this one by checking for a unique class or ID
        if (sectionElement && sectionElement.matches && sectionElement.matches('.network-bar-section')) {
          console.log('[NetworkBar] Shopify section:load event for this section.');
          cleanupNetworkBar();
          initializeNetworkBar();
        }
      });
      document.addEventListener('shopify:section:unload', function(event) {
        const sectionElement = event.target;
        if (sectionElement && sectionElement.matches && sectionElement.matches('.network-bar-section')) {
          console.log('[NetworkBar] Shopify section:unload event for this section.');
          cleanupNetworkBar();
        }
      });
    }

    // Standard DOMContentLoaded for initial page load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeNetworkBar);
    } else {
      // DOMContentLoaded has already fired
      initializeNetworkBar();
    }

  })();
</script>

{% schema %}
{
  "name": "Network Bar",
  "tag": "section",
  "class": "network-bar-section",
  "settings": [
    {
      "type": "header",
      "content": "Logo Settings"
    },
    {
      "type": "image_picker",
      "id": "logo_image",
      "label": "Logo Image",
      "info": "Recommended size: 200x80px or larger. Will be automatically optimized."
    },
    {
      "type": "text",
      "id": "logo_alt",
      "label": "Logo Alt Text",
      "default": "Store Name",
      "info": "Describes the logo for screen readers. Leave blank to use store name."
    },
    {
      "type": "url",
      "id": "logo_url",
      "label": "Logo Link",
      "info": "Where the logo should link to. Leave blank for homepage."
    },
    {
      "type": "header",
      "content": "Style Settings"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background",
      "info": "Choose a solid color or gradient for the navigation background."
    },
    {
      "type": "checkbox",
      "id": "sticky_nav",
      "label": "Sticky Navigation",
      "info": "Keep the navigation bar visible when scrolling.",
      "default": true
    },
    {
      "type": "header",
      "content": "Accessibility Translations"
    },
    {
      "type": "text",
      "id": "navigation_label",
      "label": "Navigation ARIA Label",
      "default": "Brand navigation",
      "info": "ARIA label for the main navigation element."
    },
    {
      "type": "text",
      "id": "menu_label",
      "label": "Dropdown Menu ARIA Label Suffix",
      "default": "menu",
      "info": "Suffix for dropdown button ARIA labels (e.g., 'More menu')."
    },
    {
      "type": "header",
      "content": "Language Switcher"
    },
    {
      "type": "checkbox",
      "id": "enable_language_selector",
      "label": "Enable Language Selector",
      "info": "Show a language switcher when multiple languages are available.",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "brand_link",
      "name": "Brand Link",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Link Text",
          "default": "Link",
          "info": "The text that appears in the navigation."
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link URL",
          "info": "Where this link should go when clicked."
        }
      ]
    },
    {
      "type": "dropdown",
      "name": "Dropdown Menu",
      "settings": [
        {
          "type": "paragraph",
          "content": "Create a dropdown menu with multiple links. Supports up to 9 links. Only fill out the links you need - empty ones won't appear."
        },
        {
          "type": "text",
          "id": "dropdown_title",
          "label": "Dropdown Title",
          "default": "More"
        },
        { "type": "header", "content": "Dropdown Links (1-5)" },
        { "type": "text", "id": "dropdown_link_1_title", "label": "Link 1 Title", "default": "Sub Link 1" },
        { "type": "url", "id": "dropdown_link_1_url", "label": "Link 1 URL" },
        { "type": "text", "id": "dropdown_link_2_title", "label": "Link 2 Title", "default": "Sub Link 2" },
        { "type": "url", "id": "dropdown_link_2_url", "label": "Link 2 URL" },
        { "type": "text", "id": "dropdown_link_3_title", "label": "Link 3 Title", "default": "Sub Link 3" },
        { "type": "url", "id": "dropdown_link_3_url", "label": "Link 3 URL" },
        { "type": "text", "id": "dropdown_link_4_title", "label": "Link 4 Title", "default": "Sub Link 4" },
        { "type": "url", "id": "dropdown_link_4_url", "label": "Link 4 URL" },
        { "type": "text", "id": "dropdown_link_5_title", "label": "Link 5 Title", "default": "Sub Link 5" },
        { "type": "url", "id": "dropdown_link_5_url", "label": "Link 5 URL" },
        { "type": "header", "content": "Dropdown Links (6-9)" },
        { "type": "text", "id": "dropdown_link_6_title", "label": "Link 6 Title" },
        { "type": "url", "id": "dropdown_link_6_url", "label": "Link 6 URL" },
        { "type": "text", "id": "dropdown_link_7_title", "label": "Link 7 Title" },
        { "type": "url", "id": "dropdown_link_7_url", "label": "Link 7 URL" },
        { "type": "text", "id": "dropdown_link_8_title", "label": "Link 8 Title" },
        { "type": "url", "id": "dropdown_link_8_url", "label": "Link 8 URL" },
        { "type": "text", "id": "dropdown_link_9_title", "label": "Link 9 Title" },
        { "type": "url", "id": "dropdown_link_9_url", "label": "Link 9 URL" }
      ]
    }
  ],
  "presets": [
    {
      "name": "Network Bar",
      "blocks": [
        {
          "type": "brand_link",
          "settings": {
            "title": "MikeCrack",
            "url": "/"
          }
        },
        {
          "type": "brand_link",
          "settings": {
            "title": "Club América",
            "url": "https://www.amazon.com/stores/page/F2F9A1F1-9ABA-4F21-9BC7-EB08A5B49F23?channel=ClubAmerica"
          }
        },
        {
          "type": "brand_link",
          "settings": {
            "title": "Frida Kahlo",
            "url": "https://www.amazon.com/stores/page/0D1F863D-92A5-4E1E-B1AD-79C761336119?channel=FridaKahlo"
          }
        },
        {
          "type": "dropdown",
          "settings": {
            "dropdown_title": "More",
            "dropdown_link_1_title": "Attack on Titan",
            "dropdown_link_1_url": "https://www.amazon.com/stores/page/E46F48C5-D555-46FF-BDE2-A6C924F433B0?channel=AOT",
            "dropdown_link_2_title": "CMLL",
            "dropdown_link_2_url": "https://www.amazon.com/cmll",
            "dropdown_link_3_title": "Dead Kennedys",
            "dropdown_link_3_url": "https://www.amazon.com/stores/page/2F3BB934-A179-48D7-A9B3-EC6E568EB0B8?channel=DeadKennedys",
            "dropdown_link_4_title": "Gigantosaurus",
            "dropdown_link_4_url": "https://www.amazon.com/stores/page/8A463FFD-B478-4016-B50C-AC8592CB2BBB?ingress=2&visitId=ffc69bb5-b39d-4eb6-81f9-b0b227dc1909&store_ref=bl_ast_dp_brandLogo_sto&ref_=ast_bln",
            "dropdown_link_5_title": "Molang",
            "dropdown_link_5_url": "https://www.amazon.com/stores/page/6D910067-80B3-4F54-89D4-608A2315A56C?channel=Molang",
            "dropdown_link_6_title": "PlimPlim",
            "dropdown_link_6_url": "https://www.amazon.com/stores/page/3345EF04-F992-4E2B-ACCA-252A04BC7378?channel=PlimPlim",
            "dropdown_link_7_title": "Pumas",
            "dropdown_link_7_url": "https://www.amazon.com/stores/page/40210F0E-AA35-4622-8F5B-E715767048AC?ingress=3&visitId=d3b98cc7-285f-4a09-998a-0ed59d9d0b8f",
            "dropdown_link_8_title": "Rebelde",
            "dropdown_link_8_url": "https://www.amazon.com/stores/page/********-C1B2-4DB2-BC73-D95764AD05B1?channel=RBD",
            "dropdown_link_9_title": "Valiant",
            "dropdown_link_9_url": "https://www.amazon.com/stores/page/EF7501ED-2C1D-4EBA-9CD9-684ABBF1FC77?channel=Valiant"
          }
        }
      ]
    }
  ]
}
{% endschema %}
