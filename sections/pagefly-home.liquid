<script>
	window.__pageflyProducts = window.__pageflyProducts || {};
	{% unless product.id == null %}window.__pageflyProducts["{{product.id}}"] = {
		id: {{ product.id | json }},
		handle: {{ product.handle | json }},
		title: {{ product.title | json }},
		type: {{ product.type | json }},
		url: {{ product.url | within: collection | json }},
		vendor: {{ product.vendor | json }},
		variants: {{ product.variants | json }},
		options: {{ product.options | json }},
		media: {{ product.media | json }},
		has_only_default_variant: {{ product.has_only_default_variant | json }},
		options_with_values: {{ product.options_with_values | json }},
		selected_variant: {{ product.selected_variant | json }},
		selected_or_first_available_variant: {{ product.selected_or_first_available_variant | json }},
		tags: {{ product.tags | json }},
		template_suffix: {{ product.template_suffix | json }},
		featured_image: {{ product.featured_image | json }},
		featured_media: {{ product.featured_media | json }},
		images: {{ product.images | json }},
		quantity: {% assign quantity = '' %}{% for variant in product.variants %}{% assign quantity = quantity | append: variant.id | append: ':' | append: variant.inventory_quantity | append: ',' %}{% endfor %}{% assign quantity = quantity | split: ','%}{{quantity | json }}
	};{% endunless %}
	</script>{% if collection_375299768501 == null %}{%- assign collection_375299768501 = collections["frontpage"] -%}{% endif %} {% if collection_375573348533 == null %}{%- assign collection_375573348533 = collections["kids-clothing"] -%}{% endif %} {% if collection_375573479605 == null %}{%- assign collection_375573479605 = collections["kids-t-shirts"] -%}{% endif %} {% if collection_375573872821 == null %}{%- assign collection_375573872821 = collections["shoes"] -%}{% endif %} {% if collection_375573545141 == null %}{%- assign collection_375573545141 = collections["home-living"] -%}{% endif %}<script>
	window.__pageflyCollections = window.__pageflyCollections || {};
	window.__pageflyCollections["375299768501"] = {{collection_375299768501 | json }};
window.__pageflyCollections["375573348533"] = {{collection_375573348533 | json }};
window.__pageflyCollections["375573479605"] = {{collection_375573479605 | json }};
window.__pageflyCollections["375573872821"] = {{collection_375573872821 | json }};
window.__pageflyCollections["375573545141"] = {{collection_375573545141 | json }};
	</script><div data-pf-editor-version="gen-2" class="sc-ktJcvw iZCSon __pf __pf_ze6DhPTU" id="__pf"><div data-pf-type="Body" class="sc-fAGzVM kCwfYM pf-5_"><div data-pf-type="Layout" class="sc-bJBfJu gmOWvi pf-6_"><section data-section-id="pf-208f" data-pf-type="FlexSection" class="sc-kyDkUr gQpKsL pf-7_ pf-container-1 pf-color-scheme-1"><div class="sc-hCrSsE fbnsOv pf-flex-section"><img alt="header-mike-crack-desktop-01.png__PID:6ef9591c-39e7-4964-88ae-87d0d9830167" src="https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-desktop-01.png?v=1747430265&width=2048" srcSet="https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-desktop-01.png?v=1747430265&width=375 375w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-desktop-01.png?v=1747430265&width=550 550w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-desktop-01.png?v=1747430265&width=768 768w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-desktop-01.png?v=1747430265&width=1024 1024w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-desktop-01.png?v=1747430265&width=1280 1280w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-desktop-01.png?v=1747430265&width=1440 1440w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-desktop-01.png?v=1747430265&width=1680 1680w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-desktop-01.png?v=1747430265&width=1800 1800w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-desktop-01.png?v=1747430265&width=1920 1920w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-desktop-01.png?v=1747430265&width=2048 2048w" fetchPriority="low" decoding="async" loading="lazy" style="--pf-image-ratio:9.22" sizes="75vw" data-pf-type="Image5" class="sc-sasBe kjSXxK pf-8_ pf-image-1 pf-hide" store="[object Object]"/><img alt="header-mike-crack-mobile.png__PID:0e07d605-d84f-47c1-9735-58cc81867173" src="https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-mobile.png?v=1747429788&width=2048" srcSet="https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-mobile.png?v=1747429788&width=375 375w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-mobile.png?v=1747429788&width=550 550w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-mobile.png?v=1747429788&width=768 768w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-mobile.png?v=1747429788&width=1024 1024w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-mobile.png?v=1747429788&width=1280 1280w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-mobile.png?v=1747429788&width=1440 1440w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-mobile.png?v=1747429788&width=1680 1680w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-mobile.png?v=1747429788&width=1800 1800w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-mobile.png?v=1747429788&width=1920 1920w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/header-mike-crack-mobile.png?v=1747429788&width=2048 2048w" fetchPriority="low" decoding="async" loading="lazy" style="--pf-image-ratio:3.6" sizes="75vw" data-pf-type="Image5" class="sc-sasBe kjSXxK pf-9_ pf-image-1 pf-lg-hide pf-md-hide pf-sm-hide" store="[object Object]"/></div></section><section data-section-id="pf-dd89" data-pf-type="FlexSection" class="sc-kyDkUr gQpKsL pf-10_ pf-color-scheme-1"><div class="sc-hCrSsE fbnsOv pf-flex-section"><div data-pf-type="FlexBlock" class="sc-bRilDX eDlaIl pf-11_"><div data-pf-type="Slideshow" class="sc-fhzEvr iabUTU pf-12_"><div data-id="slider-8fe7dd89-e742-4f24-b02d-59453591ba09-all" class="pf-slider scrollfix" style="--ss-xs:1;--ss-sm:1;--ss-md:1;--ss-lg:1"><div class="pf-slide"><div class="sc-fvwkrp kMhgOM pf-13_" data-pf-type="SlideshowSlide"><a href="/collections/all-mikecrack-products" target="_self" data-pf-type="Button2" class="sc-dNsUpz dhOssz pf-14_ pf-button-2">🔥 Shop now</a><h3 data-pf-type="Heading2" class="sc-kRRxPL kSaugo pf-16_ pf-heading-1-h3 pf-lg-hide pf-md-hide pf-hide">Mikecrack Mania 🎉</h3><p class="sc-EROOM ZMPII pf-18_ pf-text-2 pf-lg-hide pf-md-hide pf-hide" data-pf-type="Paragraph4"><input type="checkbox" id="compact-5f24302d-5945-4591-ba09-da8ec54a8d82"/><span class="pf-paragraph-content ">Accessories Every Super Fan Needs! 🐶✨🎒</span></p></div></div><button type="button" style="visibility:visible" aria-label="Previous" class="pf-slider-prev none hide"></button><button type="button" style="visibility:visible" aria-label="Next" class="pf-slider-next none hide"></button></div></div><h3 data-pf-type="Heading2" class="sc-kRRxPL kSaugo pf-21_ pf-heading-1-h3">✨ New Arrivals</h3><div style="--s-xs:8px;--s-sm:15px" data-pf-type="ProductList2" class="sc-bdOgOc sc-edLa-Dd fvoHdV kKuTbA pf-23_"><div class="pf-slider pf-c-lt" style="--ss-xs:2;--ss-sm:2;--ss-md:4;--ss-lg:4" data-slider="{'navStyle':'nav-style-1','maxHeight':true,'listLayout':{'all':0,'laptop':0,'tablet':0,'mobile':0},'slidesToShow':{'all':4,'laptop':4,'tablet':2,'mobile':2},'slidesToScroll':{'all':4,'laptop':4,'tablet':2,'mobile':2},'paginationStyle':'pagination-style-1','displayPartialItems':{'all':false,'laptop':false,'tablet':false,'mobile':false},'spacing':{'all':'30px','laptop':'30px','tablet':'30px','mobile':'16px'}}">{% assign collection_375573479605 = collections["kids-t-shirts"] %}{% assign defaultProduct = product %}{% paginate collection_375573479605.products by 4 %}{% for product in collection_375573479605.products %}{% assign product_index = forloop.index | minus: 1 %}<div class="pf-slide pf-c"><script>
				window.__pageflyProducts = window.__pageflyProducts || {};
				window.__pageflyProducts["{{product.id}}"] = {
					id: {{product.id | json }},
					handle: {{product.handle | json }},
					title: {{product.title | json }},
					type: {{product.type | json }},
					url: {{product.url | within: collection  | json }},
					vendor: {{product.vendor | json }},
					variants: {{product.variants | json }},
					options: {{product.options | json }},
					media: {{product.media | json }},
					has_only_default_variant: {{product.has_only_default_variant | json }},
					options_with_values: {{product.options_with_values | json }},
					selected_variant: {{product.selected_variant | json }},
					selected_or_first_available_variant: {{product.selected_or_first_available_variant | json }},
					tags: {{product.tags | json }},
					template_suffix: {{product.template_suffix | json }},
					featured_image: {{product.featured_image | json }},
					featured_media: {{product.featured_media | json }},
					images: {{product.images | json }},
					quantity: {% assign quantity = '' %}{% for variant in product.variants %}{% assign quantity = quantity | append: variant.id | append: ':' | append: variant.inventory_quantity | append: ',' %}{% endfor %}{% assign quantity = quantity | split: ','%}{{quantity | json }}

				};</script><div data-product-id="{{ product.id }}" data-pf-type="ProductBox" class="sc-enkJyZ dMMyzv pf-24_">{% if product != null %}{% form "product", product, data-productid: product.id, class: "pf-product-form" %}<div data-product-id="{{ product.id }}" data-media-id="{% liquid
    assign min = 10000000000
    assign max = 99999999999
    assign diff = max | minus: min
    assign number = "now" | date: "%N" | modulo: diff | plus: min
  %}{{number}}" data-pf-type="ProductMedia3" class="sc-cDspyv jhZIdA pf-25_">
                      {% assign media = product.media %}
                      {% assign featured_media = product.featured_media %}
                      {% assign _product = product %}
                      {% assign first_3d_model = product.media | where: "media_type", "model" | first %}
                  <div class="sc-jhlPQp jqVnNh   pf-lg-hide-list pf-md-hide-list pf-sm-hide-list pf-xs-hide-list product-media2-inner product-media-loading"><div class="sc-eLtRpQ iyLOoF pmw pf-main-media-wrapper"><div data-pf-type="MediaMain3" class="sc-ZbTay eWGaxb pf-26_ pf-main-media"><div class="sc-ha-dNQk busYtL">{% if media.size == 0 %}<div class="pf-media-slider scrollfix">{% assign temp_placeholder = 'collection-1' | placeholder_svg_tag: 'pf-product-placeholder' %}{{ temp_placeholder | replace: 'class="pf-product-placeholder"', 'class="pf-product-placeholder" height="" preserveAspectRatio="xMidYMid slice"' }}</div>{% else %}{% assign assigned_product = product %}{% assign selected_variant = assigned_product.selected_variant %}{% assign media_item_selected = media | first %}{% assign media_item_selected = assigned_product.featured_media %}{% if selected_variant and selected_variant.featured_media %}{% assign media_item_selected = selected_variant.featured_media %}{% endif %}{% assign default_variant_media_id = media_item_selected.id %}<div class="pf-media-slider scrollfix" data-id="slider-ba09da8e-c54a-4d82-a735-6b8d57ec923c">{% assign temp_media = '' %}{% assign target_media_item = '' %}{% capture tmp_loading %}{% for media_item in media %}
                {% assign pf_temp_loading = "lazy" %}
                {% assign pf_temp_fetchPriority = "low" %}
                {% capture media_template %}<div class="pf-slide-main-media" data-media-type="{{ media_item.media_type }}" data-media-id="{{ media_item.id }}">{% case media_item.media_type %}
                  {% when 'model' %}<div class="pf-media-wrapper" style="aspect-ratio:16 / 9">{{ media_item | media_tag }}<div class="pf-mask"><span><svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.6" d="M14.1301 3.27971L9.00008 0.319707C8.69604 0.144171 8.35115 0.0517578 8.00008 0.0517578C7.649 0.0517578 7.30411 0.144171 7.00008 0.319707L1.88008 3.31971C1.5719 3.49759 1.31675 3.75447 1.14097 4.06385C0.965185 4.37323 0.875124 4.72391 0.880077 5.07971V10.9997C0.875124 11.3555 0.965185 11.7062 1.14097 12.0156C1.31675 12.3249 1.5719 12.5818 1.88008 12.7597L7.00008 15.7597C7.30411 15.9352 7.649 16.0277 8.00008 16.0277C8.35115 16.0277 8.69604 15.9352 9.00008 15.7597L14.1201 12.7597C14.4283 12.5818 14.6834 12.3249 14.8592 12.0156C15.035 11.7062 15.125 11.3555 15.1201 10.9997V4.99971C15.119 4.65165 15.0271 4.30991 14.8535 4.00825C14.6798 3.7066 14.4305 3.45547 14.1301 3.27971ZM7.73008 14.3797L2.61008 11.3797C2.54095 11.34 2.48167 11.2852 2.43666 11.2194C2.39164 11.1536 2.36205 11.0785 2.35008 10.9997V4.99971C2.3504 4.90601 2.37556 4.81407 2.42299 4.73327C2.47042 4.65246 2.53843 4.58567 2.62008 4.53971L7.74008 1.53971C7.82065 1.49319 7.91204 1.4687 8.00508 1.4687C8.09811 1.4687 8.18951 1.49319 8.27008 1.53971L13.3901 4.53971L8.67008 7.21971C8.45972 7.33976 8.28614 7.51498 8.16806 7.72646C8.04999 7.93793 7.9919 8.17764 8.00008 8.41971V14.4197C7.91475 14.4413 7.8254 14.4413 7.74008 14.4197L7.73008 14.3797Z" fill="black"></path></svg></span></div></div>{% when 'external_video' %}<div class="pf-media-wrapper" data-video-host="{{ media_item.host }}" style="aspect-ratio:16 / 9">{{ media_item | media_tag }}<div class="pf-mask-iframe" data-play="false"></div></div>{% when 'video' %}<div class="pf-media-wrapper" style="aspect-ratio:16 / 9">{{ media_item | media_tag: image_size: '1080x' }}</div>{% else %}
                {% assign base_url = media_item | image_url %}
                {% assign widths = "375, 550, 768, 1024, 1280, 1440, 1680, 1800, 1920, 2048" | split: ', ' %}
                {% assign srcset_values = "" %}
                
                {% for width in widths %}
                  {% assign image_url = base_url | append: "&width=" | append: width %}
                  {% assign srcset_values = srcset_values | append: image_url | append: ' ' | append: width | append: 'w,' %}
                {% endfor %}

                {% if srcset_values != "" %}
                  {% assign srcset_values = srcset_values | append: '#END' %}
                  {% assign srcset_values = srcset_values | remove: ',#END' %}
                {% endif %}
              <img alt="{{ media_item.alt }}" data-pf-temp-loading="{{ pf_temp_loading }}" class="sc-ffZBnV diSUjT active" data-action="0" style="--pf-image-ratio:{{ media_item.aspect_ratio }}" width="{{ media_item.width }}" height="{{ media_item.height }}" src="{{ media_item | image_url | append: "&width=" | append: "2048" }}" srcSet="{{ srcset_values }}" sizes="(min-width: 1200px) 25vw, (min-width: 1025px) and (max-width: 1199px) 25vw, (min-width: 768px) and (max-width: 1024px) 50vw, (max-width: 767px) 50vw" data-fetchPriority="{{ pf_temp_fetchPriority }}" decoding="async"/>{% endcase %}</div>{% endcapture %}{% if forloop.first and default_variant_media_id and default_variant_media_id != media_item.id  %}{% assign temp_media = media_template | replace: 'data-pf-temp-loading', 'loading' | replace: 'data-fetchPriority', 'fetchpriority' | prepend: '<template>' | append: '</template>' %}{% assign media_template = 'PF_TARGET_VARIANT PF_FEATURED_MEDIA_NEED_REPLACE' %}{% endif %} {{ media_template }}{% if default_variant_media_id == media_item.id %}{% assign target_media_item = media_template | replace: 'data-media-id', 'data-media-temp-id' %}{% endif %}{% endfor %} {% endcapture %} {% assign _tmp_loading = tmp_loading | replace: 'data-pf-temp-loading', 'loading' | replace: 'data-fetchPriority', 'fetchpriority' | replace: 'PF_FEATURED_MEDIA_NEED_REPLACE', temp_media | replace: 'PF_TARGET_VARIANT', target_media_item %}{% echo _tmp_loading | replace: 'loading="standard"', '' %}</div>{% endif %}</div></div></div><div class="sc-cYYudL bioLUo pf-27_ pf-list-media pf-hide pf-sm-hide pf-md-hide pf-lg-hide" data-pf-type="MediaList2"><div class="pf-media-slider scrollfix " style="--dpi-xs:0%;--gap-xs:10px" data-id="slider-09da8ec5-4a8d-42e7-b56b-8d57ec923c76">{% for media_item in media %}
              {% assign media_type = media_item.media_type %}<div class="sc-jwWawS VidLN pf-28_ pf-slide-list-media" data-img-id="{{ media_item.id }}" data-media-type="{{ media_item.media_type }}" data-pf-type="MediaItem2">
          {% assign base_url = media_item | image_url %}
          {% assign widths = "150, 250, 350, 450" | split: ', ' %}
          {% assign srcset_values = "" %}
          
          {% for width in widths %}
            {% assign image_url = base_url | append: "&width=" | append: width %}
            {% assign srcset_values = srcset_values | append: image_url | append: ' ' | append: width | append: 'w,' %}
          {% endfor %}
          
          {% if srcset_values != "" %}
            {% assign srcset_values = srcset_values | append: '#END' %}
            {% assign srcset_values = srcset_values | remove: ',#END' %}
          {% endif %}
        <img alt="{{ media_item.alt }}" loading="lazy" src="{{ media_item | img_url: "xundefined", scale: 2}}" srcSet="{{ srcset_values }}" fetchPriority="low" decoding="async" sizes="(min-width: 1200px) 20vw, (min-width: 1025px) and (max-width: 1199px) 20vw, (min-width: 768px) and (max-width: 1024px) 20vw, (max-width: 767px) 20vw"/>{% case media_type %}
            {% when 'model' %}<span class="sc-inyWwX bHbGXm"><svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.6" d="M14.1301 3.27971L9.00008 0.319707C8.69604 0.144171 8.35115 0.0517578 8.00008 0.0517578C7.649 0.0517578 7.30411 0.144171 7.00008 0.319707L1.88008 3.31971C1.5719 3.49759 1.31675 3.75447 1.14097 4.06385C0.965185 4.37323 0.875124 4.72391 0.880077 5.07971V10.9997C0.875124 11.3555 0.965185 11.7062 1.14097 12.0156C1.31675 12.3249 1.5719 12.5818 1.88008 12.7597L7.00008 15.7597C7.30411 15.9352 7.649 16.0277 8.00008 16.0277C8.35115 16.0277 8.69604 15.9352 9.00008 15.7597L14.1201 12.7597C14.4283 12.5818 14.6834 12.3249 14.8592 12.0156C15.035 11.7062 15.125 11.3555 15.1201 10.9997V4.99971C15.119 4.65165 15.0271 4.30991 14.8535 4.00825C14.6798 3.7066 14.4305 3.45547 14.1301 3.27971ZM7.73008 14.3797L2.61008 11.3797C2.54095 11.34 2.48167 11.2852 2.43666 11.2194C2.39164 11.1536 2.36205 11.0785 2.35008 10.9997V4.99971C2.3504 4.90601 2.37556 4.81407 2.42299 4.73327C2.47042 4.65246 2.53843 4.58567 2.62008 4.53971L7.74008 1.53971C7.82065 1.49319 7.91204 1.4687 8.00508 1.4687C8.09811 1.4687 8.18951 1.49319 8.27008 1.53971L13.3901 4.53971L8.67008 7.21971C8.45972 7.33976 8.28614 7.51498 8.16806 7.72646C8.04999 7.93793 7.9919 8.17764 8.00008 8.41971V14.4197C7.91475 14.4413 7.8254 14.4413 7.74008 14.4197L7.73008 14.3797Z" fill="black"></path></svg></span>{% when 'external_video' or 'video' %}<span class="sc-inyWwX bHbGXm"><svg width="11" height="14" viewBox="0 0 11 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.6" d="M1.71795 0.719814C1.56631 0.627985 1.39299 0.578117 1.21573 0.57532C1.03847 0.572523 0.863662 0.616896 0.7092 0.703896C0.554738 0.790895 0.42618 0.917392 0.336696 1.07043C0.247212 1.22346 0.200019 1.39754 0.199951 1.57481V12.3108C0.199976 12.4926 0.249526 12.6708 0.343274 12.8265C0.437022 12.9822 0.571425 13.1094 0.732038 13.1945C0.89265 13.2795 1.0734 13.3191 1.25486 13.3092C1.43632 13.2992 1.61163 13.24 1.76195 13.1378L10.112 7.46081C10.2504 7.36662 10.363 7.23915 10.4394 7.09011C10.5158 6.94107 10.5536 6.77523 10.5492 6.6078C10.5448 6.44038 10.4985 6.27673 10.4144 6.13189C10.3303 5.98704 10.2112 5.86564 10.068 5.77881L1.71795 0.718814V0.719814Z" fill="black"></path></svg></span>{% endcase %}</div>{% endfor %}</div></div></div></div><h3 data-product-type="title" data-product-id="{{ product.id }}" data-href="{{ product.url | within: collection }}" data-pf-type="ProductTitle" class="sc-jkTopv sc-ciQpcn ivnYkW iDxSKP pf-30_ pf-heading-1-h3">{{ product.title }}</h3><div data-pf-type="ProductPrice2" class="sc-IqJhK iDIZgg pf-31_ pf-text-1"><div data-product-type="price" data-product-id="{{ product.id }}" data-product-price="true" data-pf-type="ProductPrice2Item" class="sc-eiQrVR tpBxD pf-32_ pf-text-1">{{ product.variants[0].price | money }}</div></div><button data-product-id="{{ product.id }}" data-checkout="same" data-soldout="Sold out" data-adding="Adding..." data-added="Thank you!" name="add" type="button" data-pf-type="ProductATC2" class="sc-xwvkV gFSrYE pf-34_ pf-button-1">Add To Cart<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-pf-type="Icon2" class="sc-dycZeM bYnQgk pf-35_ pf-icon2-1"><path d="M160 112c0-35.3 28.7-64 64-64s64 28.7 64 64v48H160V112zm-48 48H48c-26.5 0-48 21.5-48 48V416c0 53 43 96 96 96H352c53 0 96-43 96-96V208c0-26.5-21.5-48-48-48H336V112C336 50.1 285.9 0 224 0S112 50.1 112 112v48zm24 48a24 24 0 1 1 0 48 24 24 0 1 1 0-48zm152 24a24 24 0 1 1 48 0 24 24 0 1 1 -48 0z"></path></svg></button>{% endform %}{% endif %}</div></div>{% endfor %}{% endpaginate %}{% assign product = defaultProduct %}<button type="button" aria-label="Previous" style="visibility:hidden" class="pf-slider-prev nav-style-1"></button><button type="button" aria-label="Next" style="visibility:hidden" class="pf-slider-next nav-style-1"></button><div class="pf-slider-nav pagination-style-1"></div></div></div></div></div></section><section data-section-id="pf-923c" data-pf-type="FlexSection" class="sc-kyDkUr gQpKsL pf-36_ pf-container-1 pf-color-scheme-1"><div class="sc-hCrSsE fbnsOv pf-flex-section"><div style="--s-xs:8px;--s-sm:15px" data-pf-type="ProductList2" class="sc-bdOgOc sc-edLa-Dd cOGBiC dsSjhI pf-37_"><div class="pf-slider pf-c-lt" style="--ss-xs:1;--ss-sm:2;--ss-md:3;--ss-lg:3" data-slider="{'navStyle':'nav-style-1','maxHeight':true,'listLayout':{'all':1,'laptop':1,'tablet':1,'mobile':1},'slidesToShow':{'all':3,'laptop':3,'tablet':2,'mobile':1},'slidesToScroll':{'all':1,'laptop':1,'tablet':1,'mobile':1},'paginationStyle':'pagination-style-1','displayPartialItems':{'all':false,'laptop':false,'tablet':false,'mobile':false},'spacing':{'all':'30px','laptop':'30px','tablet':'30px','mobile':'16px'}}">{% assign collection_375299768501 = collections["frontpage"] %}{% assign defaultProduct = product %}{% paginate collection_375299768501.products by 8 %}{% for product in collection_375299768501.products %}{% assign product_index = forloop.index | minus: 1 %}<div class="pf-slide pf-c"><script>
				window.__pageflyProducts = window.__pageflyProducts || {};
				window.__pageflyProducts["{{product.id}}"] = {
					id: {{product.id | json }},
					handle: {{product.handle | json }},
					title: {{product.title | json }},
					type: {{product.type | json }},
					url: {{product.url | within: collection  | json }},
					vendor: {{product.vendor | json }},
					variants: {{product.variants | json }},
					options: {{product.options | json }},
					media: {{product.media | json }},
					has_only_default_variant: {{product.has_only_default_variant | json }},
					options_with_values: {{product.options_with_values | json }},
					selected_variant: {{product.selected_variant | json }},
					selected_or_first_available_variant: {{product.selected_or_first_available_variant | json }},
					tags: {{product.tags | json }},
					template_suffix: {{product.template_suffix | json }},
					featured_image: {{product.featured_image | json }},
					featured_media: {{product.featured_media | json }},
					images: {{product.images | json }},
					quantity: {% assign quantity = '' %}{% for variant in product.variants %}{% assign quantity = quantity | append: variant.id | append: ':' | append: variant.inventory_quantity | append: ',' %}{% endfor %}{% assign quantity = quantity | split: ','%}{{quantity | json }}

				};</script><div data-product-id="{{ product.id }}" data-pf-type="ProductBox" class="sc-enkJyZ dMMyzv pf-38_">{% if product != null %}{% form "product", product, data-productid: product.id, class: "pf-product-form" %}<div data-product-id="{{ product.id }}" data-media-id="{% liquid
    assign min = 10000000000
    assign max = 99999999999
    assign diff = max | minus: min
    assign number = "now" | date: "%N" | modulo: diff | plus: min
  %}{{number}}" data-pf-type="ProductMedia3" class="sc-cDspyv jhZIdA pf-39_">
                      {% assign media = product.media %}
                      {% assign featured_media = product.featured_media %}
                      {% assign _product = product %}
                      {% assign first_3d_model = product.media | where: "media_type", "model" | first %}
                  <div class="sc-jhlPQp jqVnNh   pf-lg-hide-list pf-md-hide-list pf-sm-hide-list pf-xs-hide-list product-media2-inner product-media-loading"><div class="sc-eLtRpQ iyLOoF pmw pf-main-media-wrapper"><div data-pf-type="MediaMain3" class="sc-ZbTay eWGaxb pf-40_ pf-main-media"><div class="sc-ha-dNQk busYtL">{% if media.size == 0 %}<div class="pf-media-slider scrollfix">{% assign temp_placeholder = 'collection-1' | placeholder_svg_tag: 'pf-product-placeholder' %}{{ temp_placeholder | replace: 'class="pf-product-placeholder"', 'class="pf-product-placeholder" height="" preserveAspectRatio="xMidYMid slice"' }}</div>{% else %}{% assign assigned_product = product %}{% assign selected_variant = assigned_product.selected_variant %}{% assign media_item_selected = media | first %}{% assign media_item_selected = assigned_product.featured_media %}{% if selected_variant and selected_variant.featured_media %}{% assign media_item_selected = selected_variant.featured_media %}{% endif %}{% assign default_variant_media_id = media_item_selected.id %}<div class="pf-media-slider scrollfix" data-id="slider-923c7699-abef-4512-a858-0cc4a5c8ea74">{% assign temp_media = '' %}{% assign target_media_item = '' %}{% capture tmp_loading %}{% for media_item in media %}
                {% assign pf_temp_loading = "lazy" %}
                {% assign pf_temp_fetchPriority = "low" %}
                {% capture media_template %}<div class="pf-slide-main-media" data-media-type="{{ media_item.media_type }}" data-media-id="{{ media_item.id }}">{% case media_item.media_type %}
                  {% when 'model' %}<div class="pf-media-wrapper" style="aspect-ratio:16 / 9">{{ media_item | media_tag }}<div class="pf-mask"><span><svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.6" d="M14.1301 3.27971L9.00008 0.319707C8.69604 0.144171 8.35115 0.0517578 8.00008 0.0517578C7.649 0.0517578 7.30411 0.144171 7.00008 0.319707L1.88008 3.31971C1.5719 3.49759 1.31675 3.75447 1.14097 4.06385C0.965185 4.37323 0.875124 4.72391 0.880077 5.07971V10.9997C0.875124 11.3555 0.965185 11.7062 1.14097 12.0156C1.31675 12.3249 1.5719 12.5818 1.88008 12.7597L7.00008 15.7597C7.30411 15.9352 7.649 16.0277 8.00008 16.0277C8.35115 16.0277 8.69604 15.9352 9.00008 15.7597L14.1201 12.7597C14.4283 12.5818 14.6834 12.3249 14.8592 12.0156C15.035 11.7062 15.125 11.3555 15.1201 10.9997V4.99971C15.119 4.65165 15.0271 4.30991 14.8535 4.00825C14.6798 3.7066 14.4305 3.45547 14.1301 3.27971ZM7.73008 14.3797L2.61008 11.3797C2.54095 11.34 2.48167 11.2852 2.43666 11.2194C2.39164 11.1536 2.36205 11.0785 2.35008 10.9997V4.99971C2.3504 4.90601 2.37556 4.81407 2.42299 4.73327C2.47042 4.65246 2.53843 4.58567 2.62008 4.53971L7.74008 1.53971C7.82065 1.49319 7.91204 1.4687 8.00508 1.4687C8.09811 1.4687 8.18951 1.49319 8.27008 1.53971L13.3901 4.53971L8.67008 7.21971C8.45972 7.33976 8.28614 7.51498 8.16806 7.72646C8.04999 7.93793 7.9919 8.17764 8.00008 8.41971V14.4197C7.91475 14.4413 7.8254 14.4413 7.74008 14.4197L7.73008 14.3797Z" fill="black"></path></svg></span></div></div>{% when 'external_video' %}<div class="pf-media-wrapper" data-video-host="{{ media_item.host }}" style="aspect-ratio:16 / 9">{{ media_item | media_tag }}<div class="pf-mask-iframe" data-play="false"></div></div>{% when 'video' %}<div class="pf-media-wrapper" style="aspect-ratio:16 / 9">{{ media_item | media_tag: image_size: '1080x' }}</div>{% else %}
                {% assign base_url = media_item | image_url %}
                {% assign widths = "375, 550, 768, 1024, 1280, 1440, 1680, 1800, 1920, 2048" | split: ', ' %}
                {% assign srcset_values = "" %}
                
                {% for width in widths %}
                  {% assign image_url = base_url | append: "&width=" | append: width %}
                  {% assign srcset_values = srcset_values | append: image_url | append: ' ' | append: width | append: 'w,' %}
                {% endfor %}

                {% if srcset_values != "" %}
                  {% assign srcset_values = srcset_values | append: '#END' %}
                  {% assign srcset_values = srcset_values | remove: ',#END' %}
                {% endif %}
              <img alt="{{ media_item.alt }}" data-pf-temp-loading="{{ pf_temp_loading }}" class="sc-ffZBnV diSUjT active" data-action="0" style="--pf-image-ratio:{{ media_item.aspect_ratio }}" width="{{ media_item.width }}" height="{{ media_item.height }}" src="{{ media_item | image_url | append: "&width=" | append: "2048" }}" srcSet="{{ srcset_values }}" sizes="(min-width: 1200px) 33vw, (min-width: 1025px) and (max-width: 1199px) 33vw, (min-width: 768px) and (max-width: 1024px) 50vw, (max-width: 767px) 75vw" data-fetchPriority="{{ pf_temp_fetchPriority }}" decoding="async"/>{% endcase %}</div>{% endcapture %}{% if forloop.first and default_variant_media_id and default_variant_media_id != media_item.id  %}{% assign temp_media = media_template | replace: 'data-pf-temp-loading', 'loading' | replace: 'data-fetchPriority', 'fetchpriority' | prepend: '<template>' | append: '</template>' %}{% assign media_template = 'PF_TARGET_VARIANT PF_FEATURED_MEDIA_NEED_REPLACE' %}{% endif %} {{ media_template }}{% if default_variant_media_id == media_item.id %}{% assign target_media_item = media_template | replace: 'data-media-id', 'data-media-temp-id' %}{% endif %}{% endfor %} {% endcapture %} {% assign _tmp_loading = tmp_loading | replace: 'data-pf-temp-loading', 'loading' | replace: 'data-fetchPriority', 'fetchpriority' | replace: 'PF_FEATURED_MEDIA_NEED_REPLACE', temp_media | replace: 'PF_TARGET_VARIANT', target_media_item %}{% echo _tmp_loading | replace: 'loading="standard"', '' %}</div>{% endif %}</div></div></div><div class="sc-cYYudL bioLUo pf-41_ pf-list-media pf-hide pf-sm-hide pf-md-hide pf-lg-hide" data-pf-type="MediaList2"><div class="pf-media-slider scrollfix " style="--dpi-xs:0%;--gap-xs:10px" data-id="slider-3c7699ab-ef75-42e8-980c-c4a5c8ea746a">{% for media_item in media %}
              {% assign media_type = media_item.media_type %}<div class="sc-jwWawS VidLN pf-42_ pf-slide-list-media" data-img-id="{{ media_item.id }}" data-media-type="{{ media_item.media_type }}" data-pf-type="MediaItem2">
          {% assign base_url = media_item | image_url %}
          {% assign widths = "150, 250, 350, 450" | split: ', ' %}
          {% assign srcset_values = "" %}
          
          {% for width in widths %}
            {% assign image_url = base_url | append: "&width=" | append: width %}
            {% assign srcset_values = srcset_values | append: image_url | append: ' ' | append: width | append: 'w,' %}
          {% endfor %}
          
          {% if srcset_values != "" %}
            {% assign srcset_values = srcset_values | append: '#END' %}
            {% assign srcset_values = srcset_values | remove: ',#END' %}
          {% endif %}
        <img alt="{{ media_item.alt }}" loading="lazy" src="{{ media_item | img_url: "xundefined", scale: 2}}" srcSet="{{ srcset_values }}" fetchPriority="low" decoding="async" sizes="(min-width: 1200px) 20vw, (min-width: 1025px) and (max-width: 1199px) 20vw, (min-width: 768px) and (max-width: 1024px) 20vw, (max-width: 767px) 20vw"/>{% case media_type %}
            {% when 'model' %}<span class="sc-inyWwX bHbGXm"><svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.6" d="M14.1301 3.27971L9.00008 0.319707C8.69604 0.144171 8.35115 0.0517578 8.00008 0.0517578C7.649 0.0517578 7.30411 0.144171 7.00008 0.319707L1.88008 3.31971C1.5719 3.49759 1.31675 3.75447 1.14097 4.06385C0.965185 4.37323 0.875124 4.72391 0.880077 5.07971V10.9997C0.875124 11.3555 0.965185 11.7062 1.14097 12.0156C1.31675 12.3249 1.5719 12.5818 1.88008 12.7597L7.00008 15.7597C7.30411 15.9352 7.649 16.0277 8.00008 16.0277C8.35115 16.0277 8.69604 15.9352 9.00008 15.7597L14.1201 12.7597C14.4283 12.5818 14.6834 12.3249 14.8592 12.0156C15.035 11.7062 15.125 11.3555 15.1201 10.9997V4.99971C15.119 4.65165 15.0271 4.30991 14.8535 4.00825C14.6798 3.7066 14.4305 3.45547 14.1301 3.27971ZM7.73008 14.3797L2.61008 11.3797C2.54095 11.34 2.48167 11.2852 2.43666 11.2194C2.39164 11.1536 2.36205 11.0785 2.35008 10.9997V4.99971C2.3504 4.90601 2.37556 4.81407 2.42299 4.73327C2.47042 4.65246 2.53843 4.58567 2.62008 4.53971L7.74008 1.53971C7.82065 1.49319 7.91204 1.4687 8.00508 1.4687C8.09811 1.4687 8.18951 1.49319 8.27008 1.53971L13.3901 4.53971L8.67008 7.21971C8.45972 7.33976 8.28614 7.51498 8.16806 7.72646C8.04999 7.93793 7.9919 8.17764 8.00008 8.41971V14.4197C7.91475 14.4413 7.8254 14.4413 7.74008 14.4197L7.73008 14.3797Z" fill="black"></path></svg></span>{% when 'external_video' or 'video' %}<span class="sc-inyWwX bHbGXm"><svg width="11" height="14" viewBox="0 0 11 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.6" d="M1.71795 0.719814C1.56631 0.627985 1.39299 0.578117 1.21573 0.57532C1.03847 0.572523 0.863662 0.616896 0.7092 0.703896C0.554738 0.790895 0.42618 0.917392 0.336696 1.07043C0.247212 1.22346 0.200019 1.39754 0.199951 1.57481V12.3108C0.199976 12.4926 0.249526 12.6708 0.343274 12.8265C0.437022 12.9822 0.571425 13.1094 0.732038 13.1945C0.89265 13.2795 1.0734 13.3191 1.25486 13.3092C1.43632 13.2992 1.61163 13.24 1.76195 13.1378L10.112 7.46081C10.2504 7.36662 10.363 7.23915 10.4394 7.09011C10.5158 6.94107 10.5536 6.77523 10.5492 6.6078C10.5448 6.44038 10.4985 6.27673 10.4144 6.13189C10.3303 5.98704 10.2112 5.86564 10.068 5.77881L1.71795 0.718814V0.719814Z" fill="black"></path></svg></span>{% endcase %}</div>{% endfor %}</div></div></div></div><h3 data-product-type="title" data-product-id="{{ product.id }}" data-href="{{ product.url | within: collection }}" data-pf-type="ProductTitle" class="sc-jkTopv sc-ciQpcn ivnYkW iDxSKP pf-44_ pf-heading-1-h3">{{ product.title }}</h3><div data-pf-type="ProductPrice2" class="sc-IqJhK iDIZgg pf-45_ pf-text-1"><div data-product-type="price" data-product-id="{{ product.id }}" data-product-price="true" data-pf-type="ProductPrice2Item" class="sc-eiQrVR tpBxD pf-46_ pf-text-1">{{ product.variants[0].price | money }}</div></div><button data-product-id="{{ product.id }}" data-checkout="same" data-soldout="Sold out" data-adding="Adding..." data-added="Thank you!" name="add" type="button" data-pf-type="ProductATC2" class="sc-xwvkV gFSrYE pf-48_ pf-button-1">Add To Cart<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" data-pf-type="Icon2" class="sc-dycZeM bYnQgk pf-49_ pf-icon2-1"><path d="M0 24C0 10.7 10.7 0 24 0H69.5c22 0 41.5 12.8 50.6 32h411c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3H170.7l5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H199.7c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5H24C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"></path></svg></button>{% endform %}{% endif %}</div></div>{% endfor %}{% endpaginate %}{% assign product = defaultProduct %}<button type="button" aria-label="Previous" style="visibility:hidden" class="pf-slider-prev nav-style-1"></button><button type="button" aria-label="Next" style="visibility:hidden" class="pf-slider-next nav-style-1"></button><div class="pf-slider-nav pagination-style-1"></div></div></div><div data-pf-type="FlexBlock" class="sc-bRilDX eDlaIl pf-50_ pf-lg-hide pf-hide"><h3 data-pf-type="Heading2" class="sc-kRRxPL kSaugo pf-51_ pf-heading-1-h3">10% discount on your&nbsp;first purchase</h3><a href="/collections/mikecrack" target="_self" data-pf-type="Button2" class="sc-dNsUpz dhOssz pf-53_ pf-button-1">BUY NOW</a></div><div data-href="https://www.mikecrack.com" data-target="_self" data-pf-type="FlexBlock" class="sc-bRilDX eDlaIl pf-55_"><img alt="BANNER-PRINCIPAL-anima-ok_1.webp__PID:3e7c9a46-1f55-4471-b5eb-0d8bc237d644" src="https://cdn.shopify.com/s/files/1/0675/0788/0101/files/BANNER-PRINCIPAL-anima-ok_1.webp?v=1747430768&width=2048" srcSet="https://cdn.shopify.com/s/files/1/0675/0788/0101/files/BANNER-PRINCIPAL-anima-ok_1.webp?v=1747430768&width=375 375w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/BANNER-PRINCIPAL-anima-ok_1.webp?v=1747430768&width=550 550w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/BANNER-PRINCIPAL-anima-ok_1.webp?v=1747430768&width=768 768w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/BANNER-PRINCIPAL-anima-ok_1.webp?v=1747430768&width=1024 1024w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/BANNER-PRINCIPAL-anima-ok_1.webp?v=1747430768&width=1280 1280w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/BANNER-PRINCIPAL-anima-ok_1.webp?v=1747430768&width=1440 1440w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/BANNER-PRINCIPAL-anima-ok_1.webp?v=1747430768&width=1680 1680w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/BANNER-PRINCIPAL-anima-ok_1.webp?v=1747430768&width=1800 1800w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/BANNER-PRINCIPAL-anima-ok_1.webp?v=1747430768&width=1920 1920w,https://cdn.shopify.com/s/files/1/0675/0788/0101/files/BANNER-PRINCIPAL-anima-ok_1.webp?v=1747430768&width=2048 2048w" fetchPriority="low" decoding="async" loading="lazy" style="--pf-image-ratio:1" sizes="75vw" data-pf-type="Image5" class="sc-sasBe kjSXxK pf-56_ pf-image-1" store="[object Object]"/></div></div></section><section data-section-id="pf-bbe3" data-pf-type="FlexSection" class="sc-kyDkUr gQpKsL pf-57_ pf-container-1 pf-color-scheme-1"><div class="sc-hCrSsE fbnsOv pf-flex-section"><h3 data-pf-type="Heading2" class="sc-kRRxPL kSaugo pf-58_ pf-heading-1-h3">🎁 Special Deals</h3><p class="sc-EROOM ZMPII pf-60_ pf-text-1 pf-hide" data-pf-type="Paragraph4"><input type="checkbox" id="compact-4dbbe354-adbc-40b6-89cc-78f1ac3e8d39"/><span class="pf-paragraph-content ">Limited-time offers just for you.</span></p><div style="--s-xs:8px;--s-sm:15px;--s-lg:3px" data-pf-type="ProductList2" class="sc-bdOgOc sc-edLa-Dd gZvzPB keZTyS pf-63_"><div class="pf-slider pf-c-lt" style="--ws-xs:60;--ss-xs:1;--ss-sm:1;--ss-md:1;--ss-lg:4" data-slider="{'navStyle':'none','maxHeight':false,'listLayout':{'all':0,'laptop':1,'tablet':1,'mobile':1},'slidesToShow':{'all':4,'laptop':1,'tablet':1,'mobile':1},'slidesToScroll':{'all':1,'laptop':1,'tablet':1,'mobile':1},'paginationStyle':'pagination-style-1','displayPartialItems':{'all':false,'laptop':false,'tablet':false,'mobile':true},'spacing':{'all':'6px','laptop':'30px','tablet':'30px','mobile':'16px'}}">{% assign collection_375573872821 = collections["shoes"] %}{% assign defaultProduct = product %}{% paginate collection_375573872821.products by 4 %}{% for product in collection_375573872821.products %}{% assign product_index = forloop.index | minus: 1 %}<div class="pf-slide pf-c"><script>
				window.__pageflyProducts = window.__pageflyProducts || {};
				window.__pageflyProducts["{{product.id}}"] = {
					id: {{product.id | json }},
					handle: {{product.handle | json }},
					title: {{product.title | json }},
					type: {{product.type | json }},
					url: {{product.url | within: collection  | json }},
					vendor: {{product.vendor | json }},
					variants: {{product.variants | json }},
					options: {{product.options | json }},
					media: {{product.media | json }},
					has_only_default_variant: {{product.has_only_default_variant | json }},
					options_with_values: {{product.options_with_values | json }},
					selected_variant: {{product.selected_variant | json }},
					selected_or_first_available_variant: {{product.selected_or_first_available_variant | json }},
					tags: {{product.tags | json }},
					template_suffix: {{product.template_suffix | json }},
					featured_image: {{product.featured_image | json }},
					featured_media: {{product.featured_media | json }},
					images: {{product.images | json }},
					quantity: {% assign quantity = '' %}{% for variant in product.variants %}{% assign quantity = quantity | append: variant.id | append: ':' | append: variant.inventory_quantity | append: ',' %}{% endfor %}{% assign quantity = quantity | split: ','%}{{quantity | json }}

				};</script><div data-product-id="{{ product.id }}" data-pf-type="ProductBox" class="sc-enkJyZ dMMyzv pf-64_">{% if product != null %}{% form "product", product, data-productid: product.id, class: "pf-product-form" %}<div data-product-id="{{ product.id }}" data-media-id="{% liquid
    assign min = 10000000000
    assign max = 99999999999
    assign diff = max | minus: min
    assign number = "now" | date: "%N" | modulo: diff | plus: min
  %}{{number}}" data-pf-type="ProductMedia3" class="sc-cDspyv jhZIdA pf-65_">
                      {% assign media = product.media %}
                      {% assign featured_media = product.featured_media %}
                      {% assign _product = product %}
                      {% assign first_3d_model = product.media | where: "media_type", "model" | first %}
                  <div class="sc-jhlPQp jqVnNh   pf-lg-hide-list pf-md-hide-list pf-sm-hide-list pf-xs-hide-list product-media2-inner product-media-loading"><div class="sc-eLtRpQ iyLOoF pmw pf-main-media-wrapper"><div data-pf-type="MediaMain3" class="sc-ZbTay eWGaxb pf-66_ pf-main-media"><div class="sc-ha-dNQk busYtL">{% if media.size == 0 %}<div class="pf-media-slider scrollfix">{% assign temp_placeholder = 'collection-1' | placeholder_svg_tag: 'pf-product-placeholder' %}{{ temp_placeholder | replace: 'class="pf-product-placeholder"', 'class="pf-product-placeholder" height="" preserveAspectRatio="xMidYMid slice"' }}</div>{% else %}{% assign assigned_product = product %}{% assign selected_variant = assigned_product.selected_variant %}{% assign media_item_selected = media | first %}{% assign media_item_selected = assigned_product.featured_media %}{% if selected_variant and selected_variant.featured_media %}{% assign media_item_selected = selected_variant.featured_media %}{% endif %}{% assign default_variant_media_id = media_item_selected.id %}<div class="pf-media-slider scrollfix" data-id="slider-80b689cc-78f1-4c3e-8d39-d33b6a24a5a4">{% assign temp_media = '' %}{% assign target_media_item = '' %}{% capture tmp_loading %}{% for media_item in media %}
                {% assign pf_temp_loading = "lazy" %}
                {% assign pf_temp_fetchPriority = "low" %}
                {% capture media_template %}<div class="pf-slide-main-media" data-media-type="{{ media_item.media_type }}" data-media-id="{{ media_item.id }}">{% case media_item.media_type %}
                  {% when 'model' %}<div class="pf-media-wrapper" style="aspect-ratio:16 / 9">{{ media_item | media_tag }}<div class="pf-mask"><span><svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.6" d="M14.1301 3.27971L9.00008 0.319707C8.69604 0.144171 8.35115 0.0517578 8.00008 0.0517578C7.649 0.0517578 7.30411 0.144171 7.00008 0.319707L1.88008 3.31971C1.5719 3.49759 1.31675 3.75447 1.14097 4.06385C0.965185 4.37323 0.875124 4.72391 0.880077 5.07971V10.9997C0.875124 11.3555 0.965185 11.7062 1.14097 12.0156C1.31675 12.3249 1.5719 12.5818 1.88008 12.7597L7.00008 15.7597C7.30411 15.9352 7.649 16.0277 8.00008 16.0277C8.35115 16.0277 8.69604 15.9352 9.00008 15.7597L14.1201 12.7597C14.4283 12.5818 14.6834 12.3249 14.8592 12.0156C15.035 11.7062 15.125 11.3555 15.1201 10.9997V4.99971C15.119 4.65165 15.0271 4.30991 14.8535 4.00825C14.6798 3.7066 14.4305 3.45547 14.1301 3.27971ZM7.73008 14.3797L2.61008 11.3797C2.54095 11.34 2.48167 11.2852 2.43666 11.2194C2.39164 11.1536 2.36205 11.0785 2.35008 10.9997V4.99971C2.3504 4.90601 2.37556 4.81407 2.42299 4.73327C2.47042 4.65246 2.53843 4.58567 2.62008 4.53971L7.74008 1.53971C7.82065 1.49319 7.91204 1.4687 8.00508 1.4687C8.09811 1.4687 8.18951 1.49319 8.27008 1.53971L13.3901 4.53971L8.67008 7.21971C8.45972 7.33976 8.28614 7.51498 8.16806 7.72646C8.04999 7.93793 7.9919 8.17764 8.00008 8.41971V14.4197C7.91475 14.4413 7.8254 14.4413 7.74008 14.4197L7.73008 14.3797Z" fill="black"></path></svg></span></div></div>{% when 'external_video' %}<div class="pf-media-wrapper" data-video-host="{{ media_item.host }}" style="aspect-ratio:16 / 9">{{ media_item | media_tag }}<div class="pf-mask-iframe" data-play="false"></div></div>{% when 'video' %}<div class="pf-media-wrapper" style="aspect-ratio:16 / 9">{{ media_item | media_tag: image_size: '1080x' }}</div>{% else %}
                {% assign base_url = media_item | image_url %}
                {% assign widths = "375, 550, 768, 1024, 1280, 1440, 1680, 1800, 1920, 2048" | split: ', ' %}
                {% assign srcset_values = "" %}
                
                {% for width in widths %}
                  {% assign image_url = base_url | append: "&width=" | append: width %}
                  {% assign srcset_values = srcset_values | append: image_url | append: ' ' | append: width | append: 'w,' %}
                {% endfor %}

                {% if srcset_values != "" %}
                  {% assign srcset_values = srcset_values | append: '#END' %}
                  {% assign srcset_values = srcset_values | remove: ',#END' %}
                {% endif %}
              <img alt="{{ media_item.alt }}" data-pf-temp-loading="{{ pf_temp_loading }}" class="sc-ffZBnV diSUjT active" data-action="0" style="--pf-image-ratio:{{ media_item.aspect_ratio }}" width="{{ media_item.width }}" height="{{ media_item.height }}" src="{{ media_item | image_url | append: "&width=" | append: "2048" }}" srcSet="{{ srcset_values }}" sizes="(min-width: 1200px) 25vw, (min-width: 1025px) and (max-width: 1199px) 75vw, (min-width: 768px) and (max-width: 1024px) 75vw, (max-width: 767px) 75vw" data-fetchPriority="{{ pf_temp_fetchPriority }}" decoding="async"/>{% endcase %}</div>{% endcapture %}{% if forloop.first and default_variant_media_id and default_variant_media_id != media_item.id  %}{% assign temp_media = media_template | replace: 'data-pf-temp-loading', 'loading' | replace: 'data-fetchPriority', 'fetchpriority' | prepend: '<template>' | append: '</template>' %}{% assign media_template = 'PF_TARGET_VARIANT PF_FEATURED_MEDIA_NEED_REPLACE' %}{% endif %} {{ media_template }}{% if default_variant_media_id == media_item.id %}{% assign target_media_item = media_template | replace: 'data-media-id', 'data-media-temp-id' %}{% endif %}{% endfor %} {% endcapture %} {% assign _tmp_loading = tmp_loading | replace: 'data-pf-temp-loading', 'loading' | replace: 'data-fetchPriority', 'fetchpriority' | replace: 'PF_FEATURED_MEDIA_NEED_REPLACE', temp_media | replace: 'PF_TARGET_VARIANT', target_media_item %}{% echo _tmp_loading | replace: 'loading="standard"', '' %}</div>{% endif %}</div></div></div><div class="sc-cYYudL bioLUo pf-67_ pf-list-media pf-hide pf-sm-hide pf-md-hide pf-lg-hide" data-pf-type="MediaList2"><div class="pf-media-slider scrollfix " style="--dpi-xs:0%;--gap-xs:10px" data-id="slider-b689cc78-f1ac-4e8d-b9d3-3b6a24a5a416">{% for media_item in media %}
              {% assign media_type = media_item.media_type %}<div class="sc-jwWawS VidLN pf-68_ pf-slide-list-media" data-img-id="{{ media_item.id }}" data-media-type="{{ media_item.media_type }}" data-pf-type="MediaItem2">
          {% assign base_url = media_item | image_url %}
          {% assign widths = "150, 250, 350, 450" | split: ', ' %}
          {% assign srcset_values = "" %}
          
          {% for width in widths %}
            {% assign image_url = base_url | append: "&width=" | append: width %}
            {% assign srcset_values = srcset_values | append: image_url | append: ' ' | append: width | append: 'w,' %}
          {% endfor %}
          
          {% if srcset_values != "" %}
            {% assign srcset_values = srcset_values | append: '#END' %}
            {% assign srcset_values = srcset_values | remove: ',#END' %}
          {% endif %}
        <img alt="{{ media_item.alt }}" loading="lazy" src="{{ media_item | img_url: "xundefined", scale: 2}}" srcSet="{{ srcset_values }}" fetchPriority="low" decoding="async" sizes="(min-width: 1200px) 20vw, (min-width: 1025px) and (max-width: 1199px) 20vw, (min-width: 768px) and (max-width: 1024px) 20vw, (max-width: 767px) 20vw"/>{% case media_type %}
            {% when 'model' %}<span class="sc-inyWwX bHbGXm"><svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.6" d="M14.1301 3.27971L9.00008 0.319707C8.69604 0.144171 8.35115 0.0517578 8.00008 0.0517578C7.649 0.0517578 7.30411 0.144171 7.00008 0.319707L1.88008 3.31971C1.5719 3.49759 1.31675 3.75447 1.14097 4.06385C0.965185 4.37323 0.875124 4.72391 0.880077 5.07971V10.9997C0.875124 11.3555 0.965185 11.7062 1.14097 12.0156C1.31675 12.3249 1.5719 12.5818 1.88008 12.7597L7.00008 15.7597C7.30411 15.9352 7.649 16.0277 8.00008 16.0277C8.35115 16.0277 8.69604 15.9352 9.00008 15.7597L14.1201 12.7597C14.4283 12.5818 14.6834 12.3249 14.8592 12.0156C15.035 11.7062 15.125 11.3555 15.1201 10.9997V4.99971C15.119 4.65165 15.0271 4.30991 14.8535 4.00825C14.6798 3.7066 14.4305 3.45547 14.1301 3.27971ZM7.73008 14.3797L2.61008 11.3797C2.54095 11.34 2.48167 11.2852 2.43666 11.2194C2.39164 11.1536 2.36205 11.0785 2.35008 10.9997V4.99971C2.3504 4.90601 2.37556 4.81407 2.42299 4.73327C2.47042 4.65246 2.53843 4.58567 2.62008 4.53971L7.74008 1.53971C7.82065 1.49319 7.91204 1.4687 8.00508 1.4687C8.09811 1.4687 8.18951 1.49319 8.27008 1.53971L13.3901 4.53971L8.67008 7.21971C8.45972 7.33976 8.28614 7.51498 8.16806 7.72646C8.04999 7.93793 7.9919 8.17764 8.00008 8.41971V14.4197C7.91475 14.4413 7.8254 14.4413 7.74008 14.4197L7.73008 14.3797Z" fill="black"></path></svg></span>{% when 'external_video' or 'video' %}<span class="sc-inyWwX bHbGXm"><svg width="11" height="14" viewBox="0 0 11 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.6" d="M1.71795 0.719814C1.56631 0.627985 1.39299 0.578117 1.21573 0.57532C1.03847 0.572523 0.863662 0.616896 0.7092 0.703896C0.554738 0.790895 0.42618 0.917392 0.336696 1.07043C0.247212 1.22346 0.200019 1.39754 0.199951 1.57481V12.3108C0.199976 12.4926 0.249526 12.6708 0.343274 12.8265C0.437022 12.9822 0.571425 13.1094 0.732038 13.1945C0.89265 13.2795 1.0734 13.3191 1.25486 13.3092C1.43632 13.2992 1.61163 13.24 1.76195 13.1378L10.112 7.46081C10.2504 7.36662 10.363 7.23915 10.4394 7.09011C10.5158 6.94107 10.5536 6.77523 10.5492 6.6078C10.5448 6.44038 10.4985 6.27673 10.4144 6.13189C10.3303 5.98704 10.2112 5.86564 10.068 5.77881L1.71795 0.718814V0.719814Z" fill="black"></path></svg></span>{% endcase %}</div>{% endfor %}</div></div></div></div><h3 data-product-type="title" data-product-id="{{ product.id }}" data-href="{{ product.url | within: collection }}" data-pf-type="ProductTitle" class="sc-jkTopv sc-ciQpcn ivnYkW iDxSKP pf-70_ pf-heading-5-h3">{{ product.title }}</h3><div data-pf-type="ProductPrice2" class="sc-IqJhK iDIZgg pf-71_ pf-text-4"><div data-product-type="price" data-product-id="{{ product.id }}" data-product-price="true" data-pf-type="ProductPrice2Item" class="sc-eiQrVR tpBxD pf-72_ pf-text-6">{{ product.variants[0].price | money }}</div></div><button data-product-id="{{ product.id }}" data-checkout="same" data-soldout="Sold out" data-adding="Adding..." data-added="Thank you!" name="add" type="button" data-pf-type="ProductATC2" class="sc-xwvkV gFSrYE pf-74_ pf-button-1">Add To Cart<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-pf-type="Icon2" class="sc-dycZeM bYnQgk pf-75_ pf-icon2-1"><path d="M160 112c0-35.3 28.7-64 64-64s64 28.7 64 64v48H160V112zm-48 48H48c-26.5 0-48 21.5-48 48V416c0 53 43 96 96 96H352c53 0 96-43 96-96V208c0-26.5-21.5-48-48-48H336V112C336 50.1 285.9 0 224 0S112 50.1 112 112v48zm24 48a24 24 0 1 1 0 48 24 24 0 1 1 0-48zm152 24a24 24 0 1 1 48 0 24 24 0 1 1 -48 0z"></path></svg></button>{% endform %}{% endif %}</div></div>{% endfor %}{% endpaginate %}{% assign product = defaultProduct %}<button type="button" aria-label="Previous" style="visibility:hidden" class="pf-slider-prev none"></button><button type="button" aria-label="Next" style="visibility:hidden" class="pf-slider-next none"></button><div class="pf-slider-nav pagination-style-1"></div></div></div></div></section><section data-section-id="pf-a5a4" data-category="Collection Suggestion" data-name="Lookbook" data-pf-type="FlexSection" class="sc-kyDkUr gQpKsL pf-76_ pf-color-scheme-1"><div class="sc-hCrSsE fbnsOv pf-flex-section"><div data-pf-type="FlexBlock" class="sc-bRilDX eDlaIl pf-77_"><div data-pf-type="FlexBlock" class="sc-bRilDX eDlaIl pf-78_"><div data-pf-type="FlexBlock" class="sc-bRilDX eDlaIl pf-79_"><h3 data-pf-type="Heading2" class="sc-kRRxPL kSaugo pf-80_ pf-heading-1-h3"><a data-link="inherit" href="/collections/iphone-cases" target="_self">IPHONE CASES</a></h3></div><div data-pf-type="FlexBlock" class="sc-bRilDX eDlaIl pf-82_"><h3 data-pf-type="Heading2" class="sc-kRRxPL kSaugo pf-83_ pf-heading-1-h3"><a data-link="inherit" href="/collections/samsung-cases" target="_self">SAMSUNG CASES</a></h3></div></div><div data-pf-type="FlexBlock" class="sc-bRilDX eDlaIl pf-85_"><h3 data-pf-type="Heading2" class="sc-kRRxPL kSaugo pf-86_ pf-heading-1-h3"><a data-link="inherit" href="/collections/mens-hoodies" target="_self">Mens Hoodies</a></h3><a href="/collections/mens-hoodies" target="_self" data-pf-type="Button2" class="sc-dNsUpz dhOssz pf-88_ pf-button-1">BROWSE NOW</a></div><div data-pf-type="FlexBlock" class="sc-bRilDX eDlaIl pf-90_"><p class="sc-knuQPt cmQWaw pf-93_ pf-text-1" data-pf-type="Paragraph3"><first-sel>Girls T-Shirts</first-sel></p><h3 data-pf-type="Heading2" class="sc-kRRxPL kSaugo pf-91_ pf-heading-1-h3">FEATURED COLLECTION</h3><a href="/collections/womens-t-shirts" target="_self" data-pf-type="Button2" class="sc-dNsUpz dhOssz pf-95_ pf-button-1 animate-grow">SHOP NOW&nbsp;<br></a></div></div></div></section><section data-section-id="pf-7313" data-pf-type="FlexSection" class="sc-kyDkUr gQpKsL pf-97_ pf-container-1 pf-color-scheme-1"><div class="sc-hCrSsE fbnsOv pf-flex-section"><h3 data-pf-type="Heading2" class="sc-kRRxPL kSaugo pf-98_ pf-heading-1-h3">🔥 Take Mikecrack Everywhere!</h3><div data-pf-type="FlexBlock" class="sc-bRilDX eDlaIl pf-100_"><div style="--s-xs:8px;--s-sm:15px" data-pf-type="ProductList2" class="sc-bdOgOc sc-edLa-Dd fvoHdV kKuTbA pf-101_"><div class="pf-slider pf-c-lt" style="--ss-xs:2;--ss-sm:2;--ss-md:4;--ss-lg:4" data-slider="{'navStyle':'nav-style-1','maxHeight':true,'listLayout':{'all':0,'laptop':0,'tablet':0,'mobile':0},'slidesToShow':{'all':4,'laptop':4,'tablet':2,'mobile':2},'slidesToScroll':{'all':4,'laptop':4,'tablet':2,'mobile':2},'paginationStyle':'pagination-style-1','displayPartialItems':{'all':false,'laptop':false,'tablet':false,'mobile':false},'spacing':{'all':'30px','laptop':'30px','tablet':'30px','mobile':'16px'}}">{% assign collection_375573545141 = collections["home-living"] %}{% assign defaultProduct = product %}{% paginate collection_375573545141.products by 4 %}{% for product in collection_375573545141.products %}{% assign product_index = forloop.index | minus: 1 %}<div class="pf-slide pf-c"><script>
				window.__pageflyProducts = window.__pageflyProducts || {};
				window.__pageflyProducts["{{product.id}}"] = {
					id: {{product.id | json }},
					handle: {{product.handle | json }},
					title: {{product.title | json }},
					type: {{product.type | json }},
					url: {{product.url | within: collection  | json }},
					vendor: {{product.vendor | json }},
					variants: {{product.variants | json }},
					options: {{product.options | json }},
					media: {{product.media | json }},
					has_only_default_variant: {{product.has_only_default_variant | json }},
					options_with_values: {{product.options_with_values | json }},
					selected_variant: {{product.selected_variant | json }},
					selected_or_first_available_variant: {{product.selected_or_first_available_variant | json }},
					tags: {{product.tags | json }},
					template_suffix: {{product.template_suffix | json }},
					featured_image: {{product.featured_image | json }},
					featured_media: {{product.featured_media | json }},
					images: {{product.images | json }},
					quantity: {% assign quantity = '' %}{% for variant in product.variants %}{% assign quantity = quantity | append: variant.id | append: ':' | append: variant.inventory_quantity | append: ',' %}{% endfor %}{% assign quantity = quantity | split: ','%}{{quantity | json }}

				};</script><div data-product-id="{{ product.id }}" data-pf-type="ProductBox" class="sc-enkJyZ dMMyzv pf-102_">{% if product != null %}{% form "product", product, data-productid: product.id, class: "pf-product-form" %}<div data-product-id="{{ product.id }}" data-media-id="{% liquid
    assign min = 10000000000
    assign max = 99999999999
    assign diff = max | minus: min
    assign number = "now" | date: "%N" | modulo: diff | plus: min
  %}{{number}}" data-pf-type="ProductMedia3" class="sc-cDspyv jhZIdA pf-103_">
                      {% assign media = product.media %}
                      {% assign featured_media = product.featured_media %}
                      {% assign _product = product %}
                      {% assign first_3d_model = product.media | where: "media_type", "model" | first %}
                  <div class="sc-jhlPQp jqVnNh   pf-lg-hide-list pf-md-hide-list pf-sm-hide-list pf-xs-hide-list product-media2-inner product-media-loading"><div class="sc-eLtRpQ iyLOoF pmw pf-main-media-wrapper"><div data-pf-type="MediaMain3" class="sc-ZbTay eWGaxb pf-104_ pf-main-media"><div class="sc-ha-dNQk busYtL">{% if media.size == 0 %}<div class="pf-media-slider scrollfix">{% assign temp_placeholder = 'collection-1' | placeholder_svg_tag: 'pf-product-placeholder' %}{{ temp_placeholder | replace: 'class="pf-product-placeholder"', 'class="pf-product-placeholder" height="" preserveAspectRatio="xMidYMid slice"' }}</div>{% else %}{% assign assigned_product = product %}{% assign selected_variant = assigned_product.selected_variant %}{% assign media_item_selected = media | first %}{% assign media_item_selected = assigned_product.featured_media %}{% if selected_variant and selected_variant.featured_media %}{% assign media_item_selected = selected_variant.featured_media %}{% endif %}{% assign default_variant_media_id = media_item_selected.id %}<div class="pf-media-slider scrollfix" data-id="slider-82e9c9e2-971c-44ef-8d2b-32b99f3be706">{% assign temp_media = '' %}{% assign target_media_item = '' %}{% capture tmp_loading %}{% for media_item in media %}
                {% assign pf_temp_loading = "lazy" %}
                {% assign pf_temp_fetchPriority = "low" %}
                {% capture media_template %}<div class="pf-slide-main-media" data-media-type="{{ media_item.media_type }}" data-media-id="{{ media_item.id }}">{% case media_item.media_type %}
                  {% when 'model' %}<div class="pf-media-wrapper" style="aspect-ratio:16 / 9">{{ media_item | media_tag }}<div class="pf-mask"><span><svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.6" d="M14.1301 3.27971L9.00008 0.319707C8.69604 0.144171 8.35115 0.0517578 8.00008 0.0517578C7.649 0.0517578 7.30411 0.144171 7.00008 0.319707L1.88008 3.31971C1.5719 3.49759 1.31675 3.75447 1.14097 4.06385C0.965185 4.37323 0.875124 4.72391 0.880077 5.07971V10.9997C0.875124 11.3555 0.965185 11.7062 1.14097 12.0156C1.31675 12.3249 1.5719 12.5818 1.88008 12.7597L7.00008 15.7597C7.30411 15.9352 7.649 16.0277 8.00008 16.0277C8.35115 16.0277 8.69604 15.9352 9.00008 15.7597L14.1201 12.7597C14.4283 12.5818 14.6834 12.3249 14.8592 12.0156C15.035 11.7062 15.125 11.3555 15.1201 10.9997V4.99971C15.119 4.65165 15.0271 4.30991 14.8535 4.00825C14.6798 3.7066 14.4305 3.45547 14.1301 3.27971ZM7.73008 14.3797L2.61008 11.3797C2.54095 11.34 2.48167 11.2852 2.43666 11.2194C2.39164 11.1536 2.36205 11.0785 2.35008 10.9997V4.99971C2.3504 4.90601 2.37556 4.81407 2.42299 4.73327C2.47042 4.65246 2.53843 4.58567 2.62008 4.53971L7.74008 1.53971C7.82065 1.49319 7.91204 1.4687 8.00508 1.4687C8.09811 1.4687 8.18951 1.49319 8.27008 1.53971L13.3901 4.53971L8.67008 7.21971C8.45972 7.33976 8.28614 7.51498 8.16806 7.72646C8.04999 7.93793 7.9919 8.17764 8.00008 8.41971V14.4197C7.91475 14.4413 7.8254 14.4413 7.74008 14.4197L7.73008 14.3797Z" fill="black"></path></svg></span></div></div>{% when 'external_video' %}<div class="pf-media-wrapper" data-video-host="{{ media_item.host }}" style="aspect-ratio:16 / 9">{{ media_item | media_tag }}<div class="pf-mask-iframe" data-play="false"></div></div>{% when 'video' %}<div class="pf-media-wrapper" style="aspect-ratio:16 / 9">{{ media_item | media_tag: image_size: '1080x' }}</div>{% else %}
                {% assign base_url = media_item | image_url %}
                {% assign widths = "375, 550, 768, 1024, 1280, 1440, 1680, 1800, 1920, 2048" | split: ', ' %}
                {% assign srcset_values = "" %}
                
                {% for width in widths %}
                  {% assign image_url = base_url | append: "&width=" | append: width %}
                  {% assign srcset_values = srcset_values | append: image_url | append: ' ' | append: width | append: 'w,' %}
                {% endfor %}

                {% if srcset_values != "" %}
                  {% assign srcset_values = srcset_values | append: '#END' %}
                  {% assign srcset_values = srcset_values | remove: ',#END' %}
                {% endif %}
              <img alt="{{ media_item.alt }}" data-pf-temp-loading="{{ pf_temp_loading }}" class="sc-ffZBnV diSUjT active" data-action="0" style="--pf-image-ratio:{{ media_item.aspect_ratio }}" width="{{ media_item.width }}" height="{{ media_item.height }}" src="{{ media_item | image_url | append: "&width=" | append: "2048" }}" srcSet="{{ srcset_values }}" sizes="(min-width: 1200px) 25vw, (min-width: 1025px) and (max-width: 1199px) 25vw, (min-width: 768px) and (max-width: 1024px) 50vw, (max-width: 767px) 50vw" data-fetchPriority="{{ pf_temp_fetchPriority }}" decoding="async"/>{% endcase %}</div>{% endcapture %}{% if forloop.first and default_variant_media_id and default_variant_media_id != media_item.id  %}{% assign temp_media = media_template | replace: 'data-pf-temp-loading', 'loading' | replace: 'data-fetchPriority', 'fetchpriority' | prepend: '<template>' | append: '</template>' %}{% assign media_template = 'PF_TARGET_VARIANT PF_FEATURED_MEDIA_NEED_REPLACE' %}{% endif %} {{ media_template }}{% if default_variant_media_id == media_item.id %}{% assign target_media_item = media_template | replace: 'data-media-id', 'data-media-temp-id' %}{% endif %}{% endfor %} {% endcapture %} {% assign _tmp_loading = tmp_loading | replace: 'data-pf-temp-loading', 'loading' | replace: 'data-fetchPriority', 'fetchpriority' | replace: 'PF_FEATURED_MEDIA_NEED_REPLACE', temp_media | replace: 'PF_TARGET_VARIANT', target_media_item %}{% echo _tmp_loading | replace: 'loading="standard"', '' %}</div>{% endif %}</div></div></div><div class="sc-cYYudL bioLUo pf-105_ pf-list-media pf-hide pf-sm-hide pf-md-hide pf-lg-hide" data-pf-type="MediaList2"><div class="pf-media-slider scrollfix " style="--dpi-xs:0%;--gap-xs:10px" data-id="slider-e9c9e297-1cf4-4f0d-ab32-b99f3be706dc">{% for media_item in media %}
              {% assign media_type = media_item.media_type %}<div class="sc-jwWawS VidLN pf-106_ pf-slide-list-media" data-img-id="{{ media_item.id }}" data-media-type="{{ media_item.media_type }}" data-pf-type="MediaItem2">
          {% assign base_url = media_item | image_url %}
          {% assign widths = "150, 250, 350, 450" | split: ', ' %}
          {% assign srcset_values = "" %}
          
          {% for width in widths %}
            {% assign image_url = base_url | append: "&width=" | append: width %}
            {% assign srcset_values = srcset_values | append: image_url | append: ' ' | append: width | append: 'w,' %}
          {% endfor %}
          
          {% if srcset_values != "" %}
            {% assign srcset_values = srcset_values | append: '#END' %}
            {% assign srcset_values = srcset_values | remove: ',#END' %}
          {% endif %}
        <img alt="{{ media_item.alt }}" loading="lazy" src="{{ media_item | img_url: "xundefined", scale: 2}}" srcSet="{{ srcset_values }}" fetchPriority="low" decoding="async" sizes="(min-width: 1200px) 20vw, (min-width: 1025px) and (max-width: 1199px) 20vw, (min-width: 768px) and (max-width: 1024px) 20vw, (max-width: 767px) 20vw"/>{% case media_type %}
            {% when 'model' %}<span class="sc-inyWwX bHbGXm"><svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.6" d="M14.1301 3.27971L9.00008 0.319707C8.69604 0.144171 8.35115 0.0517578 8.00008 0.0517578C7.649 0.0517578 7.30411 0.144171 7.00008 0.319707L1.88008 3.31971C1.5719 3.49759 1.31675 3.75447 1.14097 4.06385C0.965185 4.37323 0.875124 4.72391 0.880077 5.07971V10.9997C0.875124 11.3555 0.965185 11.7062 1.14097 12.0156C1.31675 12.3249 1.5719 12.5818 1.88008 12.7597L7.00008 15.7597C7.30411 15.9352 7.649 16.0277 8.00008 16.0277C8.35115 16.0277 8.69604 15.9352 9.00008 15.7597L14.1201 12.7597C14.4283 12.5818 14.6834 12.3249 14.8592 12.0156C15.035 11.7062 15.125 11.3555 15.1201 10.9997V4.99971C15.119 4.65165 15.0271 4.30991 14.8535 4.00825C14.6798 3.7066 14.4305 3.45547 14.1301 3.27971ZM7.73008 14.3797L2.61008 11.3797C2.54095 11.34 2.48167 11.2852 2.43666 11.2194C2.39164 11.1536 2.36205 11.0785 2.35008 10.9997V4.99971C2.3504 4.90601 2.37556 4.81407 2.42299 4.73327C2.47042 4.65246 2.53843 4.58567 2.62008 4.53971L7.74008 1.53971C7.82065 1.49319 7.91204 1.4687 8.00508 1.4687C8.09811 1.4687 8.18951 1.49319 8.27008 1.53971L13.3901 4.53971L8.67008 7.21971C8.45972 7.33976 8.28614 7.51498 8.16806 7.72646C8.04999 7.93793 7.9919 8.17764 8.00008 8.41971V14.4197C7.91475 14.4413 7.8254 14.4413 7.74008 14.4197L7.73008 14.3797Z" fill="black"></path></svg></span>{% when 'external_video' or 'video' %}<span class="sc-inyWwX bHbGXm"><svg width="11" height="14" viewBox="0 0 11 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.6" d="M1.71795 0.719814C1.56631 0.627985 1.39299 0.578117 1.21573 0.57532C1.03847 0.572523 0.863662 0.616896 0.7092 0.703896C0.554738 0.790895 0.42618 0.917392 0.336696 1.07043C0.247212 1.22346 0.200019 1.39754 0.199951 1.57481V12.3108C0.199976 12.4926 0.249526 12.6708 0.343274 12.8265C0.437022 12.9822 0.571425 13.1094 0.732038 13.1945C0.89265 13.2795 1.0734 13.3191 1.25486 13.3092C1.43632 13.2992 1.61163 13.24 1.76195 13.1378L10.112 7.46081C10.2504 7.36662 10.363 7.23915 10.4394 7.09011C10.5158 6.94107 10.5536 6.77523 10.5492 6.6078C10.5448 6.44038 10.4985 6.27673 10.4144 6.13189C10.3303 5.98704 10.2112 5.86564 10.068 5.77881L1.71795 0.718814V0.719814Z" fill="black"></path></svg></span>{% endcase %}</div>{% endfor %}</div></div></div></div><h3 data-product-type="title" data-product-id="{{ product.id }}" data-href="{{ product.url | within: collection }}" data-pf-type="ProductTitle" class="sc-jkTopv sc-ciQpcn ivnYkW iDxSKP pf-108_ pf-heading-1-h3">{{ product.title }}</h3><div data-pf-type="ProductPrice2" class="sc-IqJhK iDIZgg pf-109_ pf-text-1"><div data-product-type="price" data-product-id="{{ product.id }}" data-product-price="true" data-pf-type="ProductPrice2Item" class="sc-eiQrVR tpBxD pf-110_ pf-text-1">{{ product.variants[0].price | money }}</div></div><button data-product-id="{{ product.id }}" data-checkout="same" data-soldout="Sold out" data-adding="Adding..." data-added="Thank you!" name="add" type="button" data-pf-type="ProductATC2" class="sc-xwvkV gFSrYE pf-112_ pf-button-1">Add To Cart<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-pf-type="Icon2" class="sc-dycZeM bYnQgk pf-113_ pf-icon2-1"><path d="M160 112c0-35.3 28.7-64 64-64s64 28.7 64 64v48H160V112zm-48 48H48c-26.5 0-48 21.5-48 48V416c0 53 43 96 96 96H352c53 0 96-43 96-96V208c0-26.5-21.5-48-48-48H336V112C336 50.1 285.9 0 224 0S112 50.1 112 112v48zm24 48a24 24 0 1 1 0 48 24 24 0 1 1 0-48zm152 24a24 24 0 1 1 48 0 24 24 0 1 1 -48 0z"></path></svg></button>{% endform %}{% endif %}</div></div>{% endfor %}{% endpaginate %}{% assign product = defaultProduct %}<button type="button" aria-label="Previous" style="visibility:hidden" class="pf-slider-prev nav-style-1"></button><button type="button" aria-label="Next" style="visibility:hidden" class="pf-slider-next nav-style-1"></button><div class="pf-slider-nav pagination-style-1"></div></div></div></div></div></section></div></div></div><script>
    !function(){
      window.__pagefly_page_setting__ = {"pageTitle":"Mike Crack Mania","pageType":"home","pageId":"32b99f3b-e706-4cbc-bdd0-6cee2242239c","lazyLoad":false,"forceByPassGoogleLightHouse":false,"imageLazyLoad":false,"nativeImageLazyLoad":true,"useThemeJQ":false,"sectionRootType":"Body","trackingIDs":[]};
      window.__pagefly_setting__&&(window.__pagefly_setting2__=window.__pagefly_setting__),window.__pagefly_setting__={"baseURL":"https://apps.pagefly.io","analyticsURL":"https://analytics.pagefly.io","isBackend":false,"cdnURL":"https://cdn.pagefly.io","pageflyVersion":"4.23.4","shopDomain":"3y0tss-i7.myshopify.com","elementData":{"pf-12_":{"autoPlay":true,"autoPlayDelay":5000,"loop":true,"slidesToShow":{"all":1,"laptop":1,"tablet":1,"mobile":1},"slidesToScroll":{"all":1,"laptop":1,"tablet":1,"mobile":1},"displayPartialItems":{"all":false,"laptop":false,"tablet":false,"mobile":false},"gutter":{"all":0,"laptop":0,"tablet":0,"mobile":0},"maxHeight":true,"pauseOnHover":false,"navStyle":"none","paginationStyle":"none","id":"8fe7dd89-e742-4f24-b02d-59453591ba09","_type":"Slideshow"},"pf-26_":{"hoverAction":0,"onHover":0,"clickAction":0,"navStyle":"none","paginationStyle":"none","slidesToShow":{"all":1,"laptop":1,"tablet":1,"mobile":1},"slidesToScroll":{"all":1,"laptop":1,"tablet":1,"mobile":1},"loading":"lazy","id":"ba09da8e-c54a-4d82-a735-6b8d57ec923c","_type":"MediaMain3"},"pf-27_":{"slidesToShow":{"all":5,"laptop":5,"tablet":5,"mobile":5},"slidesToScroll":{"all":1,"laptop":1,"tablet":1,"mobile":1},"displayPartialItems":{"all":false,"laptop":false,"tablet":false,"mobile":false},"navStyle":"none","listLayout":{"all":1,"laptop":1,"tablet":1,"mobile":1},"id":"09da8ec5-4a8d-42e7-b56b-8d57ec923c76","_type":"MediaList2"},"pf-29_":{"applyCondition":false,"collectionSource":"all","collectionIds":[],"tags":"","id":"8ec54a8d-82e7-456b-8d57-ec923c7699ab","_type":"ProductBadge"},"pf-25_":{"listPosition":{"all":2,"laptop":2,"tablet":2,"mobile":2},"hoverAction":0,"onHover":0,"clickAction":0,"imageSource":"featured","id":"91ba09da-8ec5-4a8d-82e7-356b8d57ec92","_type":"ProductMedia3"},"pf-34_":{"iconPos":"right","id":"e7356b8d-57ec-423c-b699-abef7512e858","_type":"ProductATC2"},"pf-40_":{"hoverAction":0,"onHover":0,"clickAction":0,"navStyle":"none","paginationStyle":"none","slidesToShow":{"all":1,"laptop":1,"tablet":1,"mobile":1},"slidesToScroll":{"all":1,"laptop":1,"tablet":1,"mobile":1},"loading":"lazy","disableSlideshow":false,"id":"923c7699-abef-4512-a858-0cc4a5c8ea74","_type":"MediaMain3"},"pf-41_":{"slidesToShow":{"all":5,"laptop":5,"tablet":5,"mobile":5},"slidesToScroll":{"all":1,"laptop":1,"tablet":1,"mobile":1},"displayPartialItems":{"all":false,"laptop":false,"tablet":false,"mobile":false},"navStyle":"none","listLayout":{"all":1,"laptop":1,"tablet":1,"mobile":1},"id":"3c7699ab-ef75-42e8-980c-c4a5c8ea746a","_type":"MediaList2"},"pf-43_":{"applyCondition":false,"collectionSource":"all","collectionIds":[],"tags":"","id":"99abef75-12e8-480c-84a5-c8ea746a1f2c","_type":"ProductBadge"},"pf-39_":{"listPosition":{"all":2,"laptop":2,"tablet":2,"mobile":2},"hoverAction":0,"onHover":0,"clickAction":0,"imageSource":"featured","id":"ec923c76-99ab-4f75-92e8-580cc4a5c8ea","_type":"ProductMedia3"},"pf-48_":{"iconPos":"right","id":"e8580cc4-a5c8-4a74-aa1f-2c4f4dbbe354","_type":"ProductATC2"},"pf-66_":{"hoverAction":0,"onHover":0,"clickAction":0,"navStyle":"none","paginationStyle":"none","slidesToShow":{"all":1,"laptop":1,"tablet":1,"mobile":1},"slidesToScroll":{"all":1,"laptop":1,"tablet":1,"mobile":1},"loading":"lazy","id":"80b689cc-78f1-4c3e-8d39-d33b6a24a5a4","_type":"MediaMain3"},"pf-67_":{"slidesToShow":{"all":5,"laptop":5,"tablet":5,"mobile":5},"slidesToScroll":{"all":1,"laptop":1,"tablet":1,"mobile":1},"displayPartialItems":{"all":false,"laptop":false,"tablet":false,"mobile":false},"navStyle":"none","listLayout":{"all":1,"laptop":1,"tablet":1,"mobile":1},"id":"b689cc78-f1ac-4e8d-b9d3-3b6a24a5a416","_type":"MediaList2"},"pf-69_":{"applyCondition":false,"collectionSource":"all","collectionIds":[],"tags":"","id":"cc78f1ac-3e8d-49d3-bb6a-24a5a416f7c7","_type":"ProductBadge"},"pf-65_":{"listPosition":{"all":2,"laptop":2,"tablet":2,"mobile":2},"hoverAction":0,"onHover":0,"clickAction":0,"imageSource":"featured","id":"bc80b689-cc78-41ac-be8d-39d33b6a24a5","_type":"ProductMedia3"},"pf-74_":{"iconPos":"right","id":"8d39d33b-6a24-45a4-96f7-c7cfdfcd3749","_type":"ProductATC2"},"pf-104_":{"hoverAction":0,"onHover":0,"clickAction":0,"navStyle":"none","paginationStyle":"none","slidesToShow":{"all":1,"laptop":1,"tablet":1,"mobile":1},"slidesToScroll":{"all":1,"laptop":1,"tablet":1,"mobile":1},"loading":"lazy","id":"82e9c9e2-971c-44ef-8d2b-32b99f3be706","_type":"MediaMain3"},"pf-105_":{"slidesToShow":{"all":5,"laptop":5,"tablet":5,"mobile":5},"slidesToScroll":{"all":1,"laptop":1,"tablet":1,"mobile":1},"displayPartialItems":{"all":false,"laptop":false,"tablet":false,"mobile":false},"navStyle":"none","listLayout":{"all":1,"laptop":1,"tablet":1,"mobile":1},"id":"e9c9e297-1cf4-4f0d-ab32-b99f3be706dc","_type":"MediaList2"},"pf-107_":{"applyCondition":false,"collectionSource":"all","collectionIds":[],"tags":"","id":"e2971cf4-ef0d-4b32-b99f-3be706dcbc7d","_type":"ProductBadge"},"pf-103_":{"listPosition":{"all":2,"laptop":2,"tablet":2,"mobile":2},"hoverAction":0,"onHover":0,"clickAction":0,"imageSource":"featured","id":"4a82e9c9-e297-4cf4-af0d-2b32b99f3be7","_type":"ProductMedia3"},"pf-112_":{"iconPos":"right","id":"0d2b32b9-9f3b-4706-9cbc-7dd06cee2242","_type":"ProductATC2"}},"shopifyProxyPath":"/apps/pagefly"},window.__pagefly_setting2__&&(window.__pagefly_setting__=function _(d,b){let c={...d||{}};for(let a of Object.keys(b))b[a]instanceof Object&&(Array.isArray(b[a])&&c[a]?Object.assign(b[a],[...b[a],...c[a]]):Object.assign(b[a],_(c[a],b[a])));return Object.assign(c||{},b),c}(__pagefly_setting2__,__pagefly_setting__),delete window.__pagefly_setting2__),window.__pagefly_setting__.moneyFormat={{shop.money_format|json}}}();
    </script>
{% schema %}
{
  "name": "PageFly Home Page",
  "settings": [
    {
      "type": "paragraph",
      "content": "For full configuration, [visit PageFly Editor](https://3y0tss-i7.myshopify.com/admin/apps/pagefly/editor?id=32b99f3b-e706-4cbc-bdd0-6cee2242239c&type=home)."
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ]
}
{% endschema %}