/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "name": "t:sections.header.name",
  "type": "header",
  "sections": {
    "announcement-bar": {
      "type": "announcement-bar",
      "blocks": {
        "announcement-bar-0": {
          "type": "announcement",
          "settings": {
            "text": "Welcome to our store",
            "link": ""
          }
        }
      },
      "block_order": [
        "announcement-bar-0"
      ],
      "settings": {
        "auto_rotate": false,
        "change_slides_speed": 5,
        "color_scheme": "scheme-4",
        "show_line_separator": true,
        "show_social": false,
        "enable_country_selector": false,
        "enable_language_selector": false
      }
    },
    "network_bar_9CH4wL": {
      "type": "network-bar",
      "blocks": {
        "brand_link_Vw7jzT": {
          "type": "brand_link",
          "settings": {
            "title": "MikeCrack",
            "url": "/pages/mikecrack"
          }
        },
        "brand_link_CwRKh9": {
          "type": "brand_link",
          "settings": {
            "title": "Club América",
            "url": "https://www.amazon.com/stores/page/F2F9A1F1-9ABA-4F21-9BC7-EB08A5B49F23?channel=ClubAmerica"
          }
        },
        "brand_link_jxQpfR": {
          "type": "brand_link",
          "settings": {
            "title": "Frida Kahlo",
            "url": "https://www.amazon.com/stores/page/0D1F863D-92A5-4E1E-B1AD-79C761336119?channel=FridaKahlo"
          }
        },
        "dropdown_hTMwKN": {
          "type": "dropdown",
          "settings": {
            "dropdown_title": "More",
            "dropdown_link_1_title": "Attack on Titan",
            "dropdown_link_1_url": "https://www.amazon.com/stores/page/E46F48C5-D555-46FF-BDE2-A6C924F433B0?channel=AOT",
            "dropdown_link_2_title": "CMLL",
            "dropdown_link_2_url": "https://www.amazon.com/cmll",
            "dropdown_link_3_title": "Dead Kennedys",
            "dropdown_link_3_url": "https://www.amazon.com/stores/page/2F3BB934-A179-48D7-A9B3-EC6E568EB0B8?channel=DeadKennedys",
            "dropdown_link_4_title": "Gigantosaurus",
            "dropdown_link_4_url": "https://www.amazon.com/stores/page/8A463FFD-B478-4016-B50C-AC8592CB2BBB?ingress=2&visitId=ffc69bb5-b39d-4eb6-81f9-b0b227dc1909&store_ref=bl_ast_dp_brandLogo_sto&ref_=ast_bln",
            "dropdown_link_5_title": "Molang",
            "dropdown_link_5_url": "https://www.amazon.com/stores/page/6D910067-80B3-4F54-89D4-608A2315A56C?channel=Molang",
            "dropdown_link_6_title": "PlimPlim",
            "dropdown_link_6_url": "https://www.amazon.com/stores/page/3345EF04-F992-4E2B-ACCA-252A04BC7378?channel=PlimPlim",
            "dropdown_link_7_title": "Pumas",
            "dropdown_link_7_url": "https://www.amazon.com/stores/page/40210F0E-AA35-4622-8F5B-E715767048AC?ingress=3&visitId=d3b98cc7-285f-4a09-998a-0ed59d9d0b8f",
            "dropdown_link_8_title": "Rebelde",
            "dropdown_link_8_url": "https://www.amazon.com/stores/page/********-C1B2-4DB2-BC73-D95764AD05B1?channel=RBD",
            "dropdown_link_9_title": "Valiant",
            "dropdown_link_9_url": "https://www.amazon.com/stores/page/EF7501ED-2C1D-4EBA-9CD9-684ABBF1FC77?channel=Valiant"
          }
        }
      },
      "block_order": [
        "brand_link_Vw7jzT",
        "brand_link_CwRKh9",
        "brand_link_jxQpfR",
        "dropdown_hTMwKN"
      ],
      "name": "Network Bar",
      "settings": {
        "logo_alt": "Store Name",
        "logo_url": "",
        "background_gradient": "",
        "sticky_nav": true,
        "navigation_label": "Brand navigation",
        "menu_label": "menu"
      }
    },
    "header": {
      "type": "header",
      "custom_css": [
        "nav {font-family: \"Dela Gothic One\";}"
      ],
      "settings": {
        "logo_position": "top-center",
        "mobile_logo_position": "center",
        "menu": "main-menu",
        "menu_type_desktop": "mega",
        "sticky_header_type": "on-scroll-up",
        "show_line_separator": false,
        "color_scheme": "scheme-4db4c6d4-f781-4c03-9350-c13df7625c71",
        "menu_color_scheme": "scheme-3",
        "enable_country_selector": true,
        "enable_language_selector": true,
        "enable_customer_avatar": true,
        "margin_bottom": 0,
        "padding_top": 0,
        "padding_bottom": 0
      }
    }
  },
  "order": [
    "announcement-bar",
    "network_bar_9CH4wL",
    "header"
  ]
}
