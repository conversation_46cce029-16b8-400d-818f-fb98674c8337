
{% if section.settings.section_id.size > 0 %}
  <!-- PageFlyGlobalSection -->
  {% render 'pagefly-main-js' %}
  {% render 'pagefly-main-css' %}
  {% include section.settings.section_id %}
{% else %}
  
    {% if request.design_mode %}
      <style>.pf-ph{font-size:14px!important;background:#fafafa;padding:20px;text-align:center;border:1px solid #f0f0f0;border-radius:4px;margin:15px}.pf-ph span{display:block;background:#e6f7ff;padding:8px 15px;border:1px solid #91d5ff}</style>
      <div class="pf-ph">
        <h3>PageFly Section</h3>
        <span>No section selected. Please select your section from the list</span>
      </div>
    {% endif %}
  
{% endif %}

{% schema %}
{
  "name": "PageFly Section",
  "settings": [
    {
      "id": "section_id",
      "type": "select",
      "label": "Sections",
      "options": [
        {
          "label": "Select section",
          "value": ""
        }
      ]
    },
    {
      "type": "paragraph",
      "content": "Find and edit your section on the [section listing](https://3y0tss-i7.myshopify.com/admin/apps/pagefly/sections)"
    },
    {
      "type": "paragraph",
      "content": "To add block to a section, please use the App Block element on PageFly section editor."
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ],
  "presets": [
    {
      "name": "PageFly Section"
    }
  ]
}
{% endschema %}
