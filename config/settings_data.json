/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "current": {
    "logo": "shopify://shop_images/logo-mikecrack-512-square.png",
    "logo_width": 140,
    "favicon": "{{ shop.brand.square_logo }}",
    "type_header_font": "dela_gothic_one_n4",
    "heading_scale": 110,
    "type_body_font": "figtree_n4",
    "body_scale": 105,
    "page_width": 1200,
    "spacing_sections": 0,
    "spacing_grid_horizontal": 28,
    "spacing_grid_vertical": 28,
    "animations_reveal_on_scroll": true,
    "animations_hover_elements": "none",
    "buttons_border_thickness": 1,
    "buttons_border_opacity": 100,
    "buttons_radius": 40,
    "buttons_shadow_opacity": 0,
    "buttons_shadow_horizontal_offset": 0,
    "buttons_shadow_vertical_offset": 4,
    "buttons_shadow_blur": 5,
    "variant_pills_border_thickness": 1,
    "variant_pills_border_opacity": 55,
    "variant_pills_radius": 40,
    "variant_pills_shadow_opacity": 0,
    "variant_pills_shadow_horizontal_offset": 0,
    "variant_pills_shadow_vertical_offset": 4,
    "variant_pills_shadow_blur": 5,
    "inputs_border_thickness": 1,
    "inputs_border_opacity": 55,
    "inputs_radius": 26,
    "inputs_shadow_opacity": 0,
    "inputs_shadow_horizontal_offset": 0,
    "inputs_shadow_vertical_offset": 0,
    "inputs_shadow_blur": 5,
    "card_style": "card",
    "card_image_padding": 16,
    "card_text_alignment": "left",
    "card_color_scheme": "scheme-1",
    "card_border_thickness": 1,
    "card_border_opacity": 100,
    "card_corner_radius": 18,
    "card_shadow_opacity": 0,
    "card_shadow_horizontal_offset": 0,
    "card_shadow_vertical_offset": 4,
    "card_shadow_blur": 5,
    "collection_card_style": "card",
    "collection_card_image_padding": 16,
    "collection_card_text_alignment": "left",
    "collection_card_color_scheme": "scheme-1",
    "collection_card_border_thickness": 1,
    "collection_card_border_opacity": 100,
    "collection_card_corner_radius": 18,
    "collection_card_shadow_opacity": 0,
    "collection_card_shadow_horizontal_offset": 0,
    "collection_card_shadow_vertical_offset": 4,
    "collection_card_shadow_blur": 5,
    "blog_card_style": "card",
    "blog_card_image_padding": 16,
    "blog_card_text_alignment": "left",
    "blog_card_color_scheme": "scheme-1",
    "blog_card_border_thickness": 1,
    "blog_card_border_opacity": 100,
    "blog_card_corner_radius": 18,
    "blog_card_shadow_opacity": 0,
    "blog_card_shadow_horizontal_offset": 0,
    "blog_card_shadow_vertical_offset": 4,
    "blog_card_shadow_blur": 5,
    "text_boxes_border_thickness": 0,
    "text_boxes_border_opacity": 10,
    "text_boxes_radius": 20,
    "text_boxes_shadow_opacity": 0,
    "text_boxes_shadow_horizontal_offset": 0,
    "text_boxes_shadow_vertical_offset": 4,
    "text_boxes_shadow_blur": 5,
    "media_border_thickness": 0,
    "media_border_opacity": 10,
    "media_radius": 20,
    "media_shadow_opacity": 0,
    "media_shadow_horizontal_offset": 0,
    "media_shadow_vertical_offset": 4,
    "media_shadow_blur": 5,
    "popup_border_thickness": 1,
    "popup_border_opacity": 10,
    "popup_corner_radius": 18,
    "popup_shadow_opacity": 0,
    "popup_shadow_horizontal_offset": 0,
    "popup_shadow_vertical_offset": 4,
    "popup_shadow_blur": 5,
    "drawer_border_thickness": 0,
    "drawer_border_opacity": 10,
    "drawer_shadow_opacity": 0,
    "drawer_shadow_horizontal_offset": 0,
    "drawer_shadow_vertical_offset": 4,
    "drawer_shadow_blur": 5,
    "badge_position": "top left",
    "badge_corner_radius": 0,
    "sale_badge_color_scheme": "scheme-4",
    "sold_out_badge_color_scheme": "scheme-1",
    "brand_headline": "",
    "brand_description": "<p></p>",
    "brand_image_width": 100,
    "social_facebook_link": "",
    "social_instagram_link": "",
    "social_youtube_link": "",
    "social_tiktok_link": "",
    "social_twitter_link": "",
    "social_snapchat_link": "",
    "social_pinterest_link": "",
    "social_tumblr_link": "",
    "social_vimeo_link": "",
    "predictive_search_enabled": true,
    "predictive_search_show_vendor": false,
    "predictive_search_show_price": false,
    "currency_code_enabled": false,
    "cart_type": "drawer",
    "show_vendor": false,
    "show_cart_note": false,
    "cart_drawer_collection": "",
    "cart_color_scheme": "scheme-1",
    "sections": {
      "main-password-header": {
        "type": "main-password-header",
        "settings": {
          "color_scheme": "scheme-3"
        }
      },
      "main-password-footer": {
        "type": "main-password-footer",
        "settings": {
          "color_scheme": "scheme-3"
        }
      }
    },
    "content_for_index": [],
    "blocks": {
      "pagefly-page-builder": {
        "type": "shopify://apps/pagefly-page-builder/blocks/app-embed/83e179f7-59a0-4589-8c66-c0dddf959200",
        "disabled": false,
        "settings": {}
      }
    },
    "color_schemes": {
      "scheme-1": {
        "settings": {
          "background": "#eff0f5",
          "background_gradient": "",
          "text": "#0e1b4d",
          "button": "#4770db",
          "button_label": "#eff0f5",
          "secondary_button_label": "#0e1b4d",
          "shadow": "#0e1b4d"
        }
      },
      "scheme-2": {
        "settings": {
          "background": "#ffffff",
          "background_gradient": "",
          "text": "#0e1b4d",
          "button": "#0e1b4d",
          "button_label": "#ffffff",
          "secondary_button_label": "#0e1b4d",
          "shadow": "#0e1b4d"
        }
      },
      "scheme-3": {
        "settings": {
          "background": "#0e1b4d",
          "background_gradient": "",
          "text": "#ffffff",
          "button": "#ffffff",
          "button_label": "#0e1b4d",
          "secondary_button_label": "#ffffff",
          "shadow": "#0e1b4d"
        }
      },
      "scheme-4": {
        "settings": {
          "background": "#4770db",
          "background_gradient": "",
          "text": "#ffffff",
          "button": "#ffffff",
          "button_label": "#4770db",
          "secondary_button_label": "#ffffff",
          "shadow": "#0e1b4d"
        }
      },
      "scheme-5": {
        "settings": {
          "background": "#e32402",
          "background_gradient": "",
          "text": "#ffffff",
          "button": "#ffffff",
          "button_label": "#e32402",
          "secondary_button_label": "#ffffff",
          "shadow": "#0e1b4d"
        }
      },
      "scheme-4db4c6d4-f781-4c03-9350-c13df7625c71": {
        "settings": {
          "background": "#47dbc7",
          "background_gradient": "linear-gradient(180deg, rgba(71, 219, 199, 1), rgba(240, 255, 253, 1) 65%)",
          "text": "#0e1b4d",
          "button": "#47dbc7",
          "button_label": "#eff0f5",
          "secondary_button_label": "#0e1b4d",
          "shadow": "#0e1b4d"
        }
      }
    }
  },
  "presets": {
    "Default": {
      "logo_width": 140,
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "background": "#eff0f5",
            "background_gradient": "",
            "text": "#0e1b4d",
            "button": "#4770db",
            "button_label": "#eff0f5",
            "secondary_button_label": "#0e1b4d",
            "shadow": "#0e1b4d"
          }
        },
        "scheme-2": {
          "settings": {
            "background": "#FFFFFF",
            "background_gradient": "",
            "text": "#0e1b4d",
            "button": "#0e1b4d",
            "button_label": "#FFFFFF",
            "secondary_button_label": "#0e1b4d",
            "shadow": "#0e1b4d"
          }
        },
        "scheme-3": {
          "settings": {
            "background": "#0e1b4d",
            "background_gradient": "",
            "text": "#FFFFFF",
            "button": "#FFFFFF",
            "button_label": "#0e1b4d",
            "secondary_button_label": "#FFFFFF",
            "shadow": "#0e1b4d"
          }
        },
        "scheme-4": {
          "settings": {
            "background": "#4770db",
            "background_gradient": "",
            "text": "#FFFFFF",
            "button": "#FFFFFF",
            "button_label": "#4770db",
            "secondary_button_label": "#FFFFFF",
            "shadow": "#0e1b4d"
          }
        },
        "scheme-5": {
          "settings": {
            "background": "#E32402",
            "background_gradient": "",
            "text": "#FFFFFF",
            "button": "#FFFFFF",
            "button_label": "#E32402",
            "secondary_button_label": "#FFFFFF",
            "shadow": "#0e1b4d"
          }
        }
      },
      "type_header_font": "archivo_n7",
      "heading_scale": 110,
      "type_body_font": "questrial_n4",
      "body_scale": 105,
      "page_width": 1200,
      "spacing_sections": 0,
      "spacing_grid_horizontal": 28,
      "spacing_grid_vertical": 28,
      "animations_reveal_on_scroll": true,
      "animations_hover_elements": "none",
      "buttons_border_thickness": 1,
      "buttons_border_opacity": 100,
      "buttons_radius": 40,
      "buttons_shadow_opacity": 0,
      "buttons_shadow_horizontal_offset": 0,
      "buttons_shadow_vertical_offset": 4,
      "buttons_shadow_blur": 5,
      "variant_pills_border_thickness": 1,
      "variant_pills_border_opacity": 55,
      "variant_pills_radius": 40,
      "variant_pills_shadow_opacity": 0,
      "variant_pills_shadow_horizontal_offset": 0,
      "variant_pills_shadow_vertical_offset": 4,
      "variant_pills_shadow_blur": 5,
      "inputs_border_thickness": 1,
      "inputs_border_opacity": 55,
      "inputs_radius": 26,
      "inputs_shadow_opacity": 0,
      "inputs_shadow_horizontal_offset": 0,
      "inputs_shadow_vertical_offset": 4,
      "inputs_shadow_blur": 5,
      "card_style": "card",
      "card_image_padding": 16,
      "card_text_alignment": "left",
      "card_color_scheme": "scheme-1",
      "card_border_thickness": 1,
      "card_border_opacity": 100,
      "card_corner_radius": 18,
      "card_shadow_opacity": 0,
      "card_shadow_horizontal_offset": 0,
      "card_shadow_vertical_offset": 4,
      "card_shadow_blur": 5,
      "collection_card_style": "card",
      "collection_card_image_padding": 16,
      "collection_card_text_alignment": "left",
      "collection_card_color_scheme": "scheme-1",
      "collection_card_border_thickness": 1,
      "collection_card_border_opacity": 100,
      "collection_card_corner_radius": 18,
      "collection_card_shadow_opacity": 0,
      "collection_card_shadow_horizontal_offset": 0,
      "collection_card_shadow_vertical_offset": 4,
      "collection_card_shadow_blur": 5,
      "blog_card_style": "card",
      "blog_card_image_padding": 16,
      "blog_card_text_alignment": "left",
      "blog_card_color_scheme": "scheme-1",
      "blog_card_border_thickness": 1,
      "blog_card_border_opacity": 100,
      "blog_card_corner_radius": 18,
      "blog_card_shadow_opacity": 0,
      "blog_card_shadow_horizontal_offset": 0,
      "blog_card_shadow_vertical_offset": 4,
      "blog_card_shadow_blur": 5,
      "text_boxes_border_thickness": 0,
      "text_boxes_border_opacity": 10,
      "text_boxes_radius": 20,
      "text_boxes_shadow_opacity": 0,
      "text_boxes_shadow_horizontal_offset": 0,
      "text_boxes_shadow_vertical_offset": 4,
      "text_boxes_shadow_blur": 5,
      "media_border_thickness": 0,
      "media_border_opacity": 10,
      "media_radius": 20,
      "media_shadow_opacity": 0,
      "media_shadow_horizontal_offset": 0,
      "media_shadow_vertical_offset": 4,
      "media_shadow_blur": 5,
      "popup_border_thickness": 1,
      "popup_border_opacity": 10,
      "popup_corner_radius": 18,
      "popup_shadow_opacity": 0,
      "popup_shadow_horizontal_offset": 0,
      "popup_shadow_vertical_offset": 4,
      "popup_shadow_blur": 5,
      "drawer_border_thickness": 0,
      "drawer_border_opacity": 10,
      "drawer_shadow_opacity": 0,
      "drawer_shadow_horizontal_offset": 0,
      "drawer_shadow_vertical_offset": 4,
      "drawer_shadow_blur": 5,
      "badge_position": "top left",
      "badge_corner_radius": 0,
      "sale_badge_color_scheme": "scheme-4",
      "sold_out_badge_color_scheme": "scheme-1",
      "brand_headline": "",
      "brand_description": "<p></p>",
      "brand_image_width": 100,
      "social_twitter_link": "",
      "social_facebook_link": "",
      "social_pinterest_link": "",
      "social_snapchat_link": "",
      "social_instagram_link": "",
      "social_tiktok_link": "",
      "social_tumblr_link": "",
      "social_youtube_link": "",
      "social_vimeo_link": "",
      "predictive_search_enabled": true,
      "predictive_search_show_vendor": false,
      "predictive_search_show_price": false,
      "currency_code_enabled": false,
      "cart_type": "drawer",
      "show_vendor": false,
      "show_cart_note": false,
      "cart_drawer_collection": "",
      "cart_color_scheme": "scheme-1",
      "sections": {
        "main-password-header": {
          "type": "main-password-header",
          "settings": {
            "color_scheme": "scheme-3"
          }
        },
        "main-password-footer": {
          "type": "main-password-footer",
          "settings": {
            "color_scheme": "scheme-3"
          }
        }
      }
    }
  }
}
